local addonName, scripty = ...

local Routines = {}

Routines.Devastation = {
    Name="Devastation",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.DevastationDefault,
    TargetingFunction=scripty.TargetingFunctions.DevastationDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Devastation.RenewingBlaze,
        scripty.Triggers.Devastation.TipTheScales,
        scripty.Triggers.Devastation.Dragonrage,
        scripty.Triggers.Devastation.FireBreath,
        scripty.Triggers.Devastation.Quell,
        scripty.Triggers.Devastation.Expunge,
        scripty.Triggers.Devastation.CauterizingFlame,
        scripty.Triggers.Devastation.ShatteringStar,
      --  scripty.Triggers.Devastation.EternitySurge,
        scripty.Triggers.Devastation.LivingFlameLeaping,
        scripty.Triggers.Devastation.Pyre,
        scripty.Triggers.Devastation.Disintegrate,
        scripty.Triggers.Devastation.EmeraldBlossomMe,
        scripty.Triggers.Devastation.VerdantEmbrace,
        scripty.Triggers.Devastation.LivingFlameHealMe,
        scripty.Triggers.Devastation.AzureStrike,
        scripty.Triggers.Devastation.EmeraldBlossom,
        scripty.Triggers.Devastation.LivingFlame,
        scripty.Triggers.Devastation.AzureStrikeSingle,
        scripty.Triggers.Devastation.LivingFlameHeal,
        scripty.Triggers.Devastation.SourceOfMagic,
        scripty.Triggers.Devastation.BlessingOfTheBronze,
        --scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
        scripty.Triggers.Devastation.DeepBreath,
        scripty.Triggers.Devastation.Hover,
        scripty.Triggers.Devastation.Landslide,

    },
    ButtonList={}
}


Routines.Outlaw = {
    Name="Outlaw",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.AssassDefault,
    TargetingFunction=scripty.TargetingFunctions.SubtletyDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Outlaw.RogueSetSingle,
        scripty.Triggers.Outlaw.RogueSetPvp,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.Prepot,
        scripty.Triggers.Subtlety.Blind2,
        scripty.Triggers.Subtlety.Sap,
        scripty.Triggers.Outlaw.KeepItRolling,
        scripty.Triggers.Outlaw.ThistleTea,
        scripty.Triggers.Outlaw.Vanish,
        scripty.Triggers.Outlaw.TricksOfTheTrade,
        scripty.Triggers.Subtlety.Evasion,
        scripty.Triggers.Subtlety.Sprint,
        -- scripty.Triggers.Outlaw.ShadowDance,
        scripty.Triggers.Subtlety.Kick,
        -- scripty.Triggers.Subtlety.Sap,
        scripty.Triggers.Subtlety.CheapShot,
        scripty.Triggers.Subtlety.KidneyShot,
        scripty.Triggers.Subtlety.Gouge,
        scripty.Triggers.Subtlety.Blind,
        scripty.Triggers.Subtlety.CrimsonVial,
        -- scripty.Triggers.Subtlety.SliceAndDice,
        -- scripty.Triggers.Subtlety.Feint,
        scripty.Triggers.Outlaw.RollTheBones,
        scripty.Triggers.Outlaw.BladeFlurry,
        scripty.Triggers.Outlaw.GhostlyStrike,
        scripty.Triggers.Outlaw.AdrenalineRush,
        scripty.Triggers.Outlaw.BetweenTheEyes,
        scripty.Triggers.Outlaw.Dispatch,
        scripty.Triggers.Outlaw.AmbushProc,
        scripty.Triggers.Outlaw.Ambush,
        scripty.Triggers.Outlaw.PistolShotOpportunity,
        scripty.Triggers.Outlaw.SinisterStrike,
        scripty.Triggers.Outlaw.PistolShot,
        scripty.Triggers.Subtlety.Stealth,
        scripty.Triggers.Subtlety.CripplingPoison,
        scripty.Triggers.Subtlety.InstantPoison,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={}
}

Routines.Subtlety = {
    Name="Subtlety",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.Default,
    TargetingFunction=scripty.TargetingFunctions.SubtletyDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Subtlety.SymbolsOfDeath,
        scripty.Triggers.Subtlety.Vanish2,
        scripty.Triggers.Subtlety.Vanish,
        scripty.Triggers.Subtlety.Evasion,
        scripty.Triggers.Subtlety.Sprint,
        scripty.Triggers.Subtlety.Kick,
        -- scripty.Triggers.Subtlety.Sap,
        -- scripty.Triggers.Subtlety.CheapShot,
        scripty.Triggers.Subtlety.Shadowstrike,
        scripty.Triggers.Subtlety.KidneyShot,
        scripty.Triggers.Subtlety.Gouge,
        scripty.Triggers.Subtlety.Blind,
        scripty.Triggers.Subtlety.CrimsonVial,
        scripty.Triggers.Subtlety.SliceAndDice,
        scripty.Triggers.Subtlety.Feint,
        scripty.Triggers.Subtlety.ShadowDance,
        scripty.Triggers.Subtlety.Flagellation,
        scripty.Triggers.Subtlety.SecretTechnique,
        scripty.Triggers.Subtlety.Rupture,
        scripty.Triggers.Subtlety.BlackPowder,
        scripty.Triggers.Subtlety.Eviscerate,
        scripty.Triggers.Subtlety.EchoingReprimand,
        scripty.Triggers.Subtlety.Sepsis,
        scripty.Triggers.Subtlety.ShurikenTornado,
        scripty.Triggers.Subtlety.ShurikenStorm,
        scripty.Triggers.Subtlety.Gloomblade,
        scripty.Triggers.Subtlety.ShurikenToss,
        scripty.Triggers.Subtlety.Stealth,
        scripty.Triggers.Subtlety.CripplingPoison,
        scripty.Triggers.Subtlety.InstantPoison,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={}
}

Routines.Assass = {
    Name="Assass",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.AssassDefault,
    TargetingFunction=scripty.TargetingFunctions.AssassDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        -- scripty.Triggers.HealthPotion,
        -- scripty.Triggers.Prepot,
        scripty.Triggers.Assass.ThistleTea,
        scripty.Triggers.Assass.ShadowDance,
        scripty.Triggers.Assass.Vanish,
        scripty.Triggers.Outlaw.TricksOfTheTrade,
        scripty.Triggers.Subtlety.Stealth,
        scripty.Triggers.Subtlety.Evasion,
        scripty.Triggers.Subtlety.Sprint,
        scripty.Triggers.Assass.StealthGarrote,
        scripty.Triggers.Subtlety.Kick,
        -- scripty.Triggers.Subtlety.CheapShot,
        scripty.Triggers.Subtlety.KidneyShot,
        scripty.Triggers.Subtlety.Gouge,
        scripty.Triggers.Subtlety.Blind,
        scripty.Triggers.Subtlety.Feint,
        scripty.Triggers.Subtlety.CrimsonVial,
        scripty.Triggers.Assass.SliceAndDice,
        scripty.Triggers.Assass.Garrote,
        scripty.Triggers.Assass.Rupture,
        scripty.Triggers.Assass.CrimsonTempest,
        scripty.Triggers.Assass.Deathmark,
        scripty.Triggers.Assass.Shiv,   
        scripty.Triggers.Assass.Envenom,
        scripty.Triggers.Assass.FanOfKnives,
        scripty.Triggers.Assass.Ambush,
        scripty.Triggers.Assass.SerratedBoneSpike,  
        scripty.Triggers.Assass.Mutilate,
        scripty.Triggers.Subtlety.CripplingPoison,
        scripty.Triggers.Assass.DeadlyPoison,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={}
}

Routines.Arcane = {
    Name="Arcane",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.ArcaneDefault,
    TargetingFunction=scripty.TargetingFunctions.Default,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Arcane.Counterspell,
        scripty.Triggers.Arcane.Prepot,
        scripty.Triggers.Arcane.SheepGhosts,
        scripty.Triggers.Arcane.PresenceOfMind,
        scripty.Triggers.Arcane.IceFloes,
        scripty.Triggers.Arcane.Spellsteal,
        scripty.Triggers.Arcane.TemporalShield,
        -- scripty.Triggers.Arcane.FrostNova,
        scripty.Triggers.Arcane.SuperNova,
        scripty.Triggers.Arcane.DragonsBreath,
        scripty.Triggers.Arcane.BlastWave,
        scripty.Triggers.Arcane.PolymorphPvP,
        scripty.Triggers.Arcane.PrismaticBarrier2,
        scripty.Triggers.Arcane.MassBarrier,
        -- scripty.Triggers.Arcane.UseTrinket,
        scripty.Triggers.Arcane.UseTrinket2,
        scripty.Triggers.Arcane.Slow,
        scripty.Triggers.Arcane.Evocation,
        scripty.Triggers.Arcane.EvocationSurgePrep,
        scripty.Triggers.Arcane.ArcaneMissilesBattery,
        scripty.Triggers.Arcane.TouchOfTheMagi,
        scripty.Triggers.Arcane.ArcaneBarrageAoE,
        scripty.Triggers.Arcane.ArcaneBarrageMana,
        scripty.Triggers.Arcane.ArcaneSurge,
        scripty.Triggers.Arcane.ArcaneMissilesClearCastCap,
        scripty.Triggers.Arcane.ArcaneOrb,
        scripty.Triggers.Arcane.ShiftingPower,
        scripty.Triggers.Arcane.ArcaneExplosion,
        scripty.Triggers.Arcane.ArcaneBlastPrecision,
        scripty.Triggers.Arcane.ArcaneBlastPrecision2,
        scripty.Triggers.Arcane.ArcaneMissilesClearCast,
        scripty.Triggers.Arcane.ArcaneBlast,
        scripty.Triggers.Arcane.Evocation,
        scripty.Triggers.Arcane.ArcaneMissiles,
        scripty.Triggers.Arcane.RemoveCurse,
        scripty.Triggers.Arcane.MirrorImage,
       -- scripty.Triggers.Arcane.Polymorph,
        scripty.Triggers.Arcane.Frostbolt,
        scripty.Triggers.Arcane.FireBlast,
        scripty.Triggers.Arcane.ConeOfCold,
        scripty.Triggers.Arcane.ArcaneExplosionLowPriority,
        scripty.Triggers.Arcane.ArcaneIntellect,
        scripty.Triggers.Arcane.PrismaticBarrier,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
        scripty.Triggers.Arcane.IceBlock,
        scripty.Triggers.Arcane.Blink,
        scripty.Triggers.Arcane.GreaterInvisibility,
        scripty.Triggers.Arcane.AlterTime,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Boomkin = {
    Name="Boomkin",
    EventHandler=scripty.EventHandlers.TreeDefault,
    DataHarvester=scripty.DataHarvesters.TreeDefault,
    TargetingFunction=scripty.TargetingFunctions.BoomkinDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Boomkin.StopCasting,
        scripty.Triggers.Boomkin.StopCastingForFrenzy,
        scripty.Triggers.Boomkin.MoonfireExplosives,
        scripty.Triggers.Tree.SleepGhosts,
        scripty.Triggers.Tree.Renewal,
        scripty.Triggers.Boomkin.Barkskin,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Boomkin.SolarBeam,
        scripty.Triggers.LaserBear.NaturesVigil,
        scripty.Triggers.Boomkin.CelestialAlignment,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        -- scripty.Triggers.Boomkin.MoonkinForm2, 
        scripty.Triggers.Boomkin.MightyBash,
        scripty.Triggers.Boomkin.BearForm,
        scripty.Triggers.Boomkin.FrenziedRegeneration,
        scripty.Triggers.Boomkin.HeartOfTheWild,
        scripty.Triggers.Boomkin.RegrowthMe,
        scripty.Triggers.LaserBear.Soothe,
        scripty.Triggers.Boomkin.RegrowthSave,
        scripty.Triggers.Boomkin.StarfallProc,
        scripty.Triggers.Boomkin.StarSurgeProc,
        scripty.Triggers.Boomkin.StarfallAvoidCap,
        scripty.Triggers.Boomkin.StarsurgeAvoidCap,
        scripty.Triggers.Boomkin.WrathForMoonkin,
        scripty.Triggers.Boomkin.Sunfire,   
        scripty.Triggers.Boomkin.StellarFlare,
        scripty.Triggers.Boomkin.Moonfire,
        scripty.Triggers.Boomkin.WrathForEclipse,
        scripty.Triggers.Boomkin.WarriorOfElune,
        -- scripty.Triggers.Boomkin.StarfireForEclipse,
        scripty.Triggers.Boomkin.FuryOfElune,
        scripty.Triggers.Boomkin.Starfall,
        scripty.Triggers.Boomkin.Starsurge,
        scripty.Triggers.Boomkin.ConvokeTheSpirits,
        scripty.Triggers.Boomkin.ConvokeTheSpirits2,
        scripty.Triggers.Boomkin.WildMushroomAOE,
        scripty.Triggers.Boomkin.WildMushroom,
        scripty.Triggers.Boomkin.WarriorOfElune2,
        scripty.Triggers.Boomkin.Starfire,
        scripty.Triggers.Tree.Wrath,
        -- scripty.Triggers.LaserBear.TigerDash,
        scripty.Triggers.LaserBear.MarkOfTheWild,
        scripty.Triggers.Oil,
        scripty.Triggers.Boomkin.RegrowthLow,
        scripty.Triggers.LaserBear.CatForm,
        -- scripty.Triggers.Boomkin.MoonkinForm,
        scripty.Triggers.Boomkin.Typhoon,
        scripty.Triggers.Boomkin.EntanglingRoots,
        scripty.Triggers.Boomkin.UrsolsVortex,
        scripty.Triggers.LootStuff,
        scripty.Triggers.LaserBear.TravelForm,
        scripty.Triggers.LaserBear.StampedingRoar,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Tree = {
    Name="Tree",
    EventHandler=scripty.EventHandlers.TreeDefault,
    DataHarvester=scripty.DataHarvesters.TreeDefault,
    TargetingFunction=scripty.TargetingFunctions.TreeDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        -- scripty.Triggers.UseTrinket,
        scripty.Triggers.Tree.Rebirth,
        scripty.Triggers.Tree.SleepGhosts,
        scripty.Triggers.Tree.CureAfflicted,
        scripty.Triggers.Boomkin.MoonfireExplosives,
        scripty.Triggers.Tree.StopCasting,
        scripty.Triggers.LaserBear.Soothe,
        scripty.Triggers.Tree.Renewal,
        scripty.Triggers.Tree.Barkskin,
        scripty.Triggers.Tree.Ironbark,
        scripty.Triggers.Tree.Innervate,
        scripty.Triggers.Tree.NaturesSwiftnessD,
        scripty.Triggers.InnervatePotion,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.LaserBear.NaturesVigil,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.Tree.LifebloomMe,
        scripty.Triggers.Tree.Incarnation,
        scripty.Triggers.Tree.NaturesCure,
        scripty.Triggers.Tree.WildGrowthProc,
        scripty.Triggers.Tree.Swiftmend,
        scripty.Triggers.Tree.Swiftmend2,
        scripty.Triggers.Tree.RegrowthProc,
        -- scripty.Triggers.Tree.Swiftmend3,
        scripty.Triggers.Tree.Lifebloom,
        scripty.Triggers.Tree.GroveGuardians,
        -- scripty.Triggers.Tree.CenarionWardTankDying,
        scripty.Triggers.Tree.WildGrowth,
        scripty.Triggers.Tree.Efflorescence,
        -- scripty.Triggers.Tree.Invigorate,
        scripty.Triggers.Tree.RejuvenationProc,
        -- scripty.Triggers.Tree.Nourish,
        -- scripty.Triggers.Tree.CenarionWard,
        -- scripty.Triggers.Tree.ConvokeTheSpirits,
        -- scripty.Triggers.Tree.Tranquility,
        scripty.Triggers.Tree.Rejuvenation2,
        scripty.Triggers.Tree.Regrowth,
        scripty.Triggers.Tree.RejuvenationBest,
        scripty.Triggers.Tree.Starsurge,
        scripty.Triggers.Tree.Sunfire,
        scripty.Triggers.LaserBear.Moonfire,
        -- scripty.Triggers.LaserBear.HeartOfTheWild,
        scripty.Triggers.Tree.LifebloomLow,
        scripty.Triggers.Tree.Starfire,
        scripty.Triggers.Tree.Wrath,
        scripty.Triggers.LootStuff,
        scripty.Triggers.LaserBear.StampedingRoar,
        -- scripty.Triggers.LaserBear.TigerDash,
        scripty.Triggers.LaserBear.MarkOfTheWild,
        scripty.Triggers.Tree.Rejuvenation2,
        scripty.Triggers.Oil,
        scripty.Triggers.LaserBear.CatForm,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
        scripty.Triggers.Boomkin.UrsolsVortex,
        scripty.Triggers.Boomkin.EntanglingRoots,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.LaserBear = {
    Name="Laser Bear",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.LaserBearDefault,
    TargetingFunction=scripty.TargetingFunctions.ProtDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Boomkin.MoonfireExplosives,
        scripty.Triggers.Tree.Hibernate,
        scripty.Triggers.LaserBear.BearForm,
        scripty.Triggers.LaserBear.BearForm2,
        scripty.Triggers.LaserBear.BearForm3,
        scripty.Triggers.LaserBear.IncapacitatingRoar,
        -- scripty.Triggers.LaserBear.Berserk,
        scripty.Triggers.LaserBear.FrenziedRegeneration,
        -- scripty.Triggers.LaserBear.RageOfTheSleeper,
        scripty.Triggers.LaserBear.RegrowthProc,
        scripty.Triggers.LaserBear.RegrowthProc2,
        scripty.Triggers.LaserBear.Renewal,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.LaserBear.FrenziedRegeneration2,
        scripty.Triggers.LaserBear.SurvivalInstincts,
        scripty.Triggers.LaserBear.SurvivalInstincts2,
        -- scripty.Triggers.LaserBear.NaturesVigil,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.LaserBear.SkullBash,
        scripty.Triggers.LaserBear.HeartOfTheWild,
        scripty.Triggers.LaserBear.RemoveCorruption,
        scripty.Triggers.LaserBear.Ironfur,
        scripty.Triggers.LaserBear.Ironfur2,
        scripty.Triggers.LaserBear.Ironfur3,
        scripty.Triggers.LaserBear.Barkskin,
        scripty.Triggers.LaserBear.Growl,
        scripty.Triggers.LaserBear.MangleProc,
        scripty.Triggers.LaserBear.MoonfireProc,
        scripty.Triggers.LaserBear.Soothe,
        scripty.Triggers.LaserBear.LunarBeam,
        scripty.Triggers.LaserBear.Moonfire,
        scripty.Triggers.LaserBear.MaulProc,
        scripty.Triggers.LaserBear.ThrashAOE,
        scripty.Triggers.LaserBear.SwipeAOE,
        scripty.Triggers.LaserBear.Thrash,
        scripty.Triggers.LaserBear.Mangle,
        scripty.Triggers.LaserBear.Maul,
        scripty.Triggers.LaserBear.ThrashSpam,
        scripty.Triggers.LaserBear.Swipe,
        scripty.Triggers.LaserBear.MoonfireSpam,
        scripty.Triggers.LaserBear.SwipeSpam,
        scripty.Triggers.LootStuff,
        scripty.Triggers.LaserBear.StampedingRoar,
       -- scripty.Triggers.LaserBear.Regrowth,
     --   scripty.Triggers.LaserBear.Prowl,
        scripty.Triggers.LaserBear.MarkOfTheWild,
        scripty.Triggers.Oil,
        scripty.Triggers.LaserBear.TigerDash,
        scripty.Triggers.LaserBear.CatForm,
        scripty.Triggers.LaserBear.BearForm4,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
        scripty.Triggers.Boomkin.UrsolsVortex,
        scripty.Triggers.Boomkin.EntanglingRoots,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Feral = {
    Name="Feral",
    EventHandler=scripty.EventHandlers.TreeDefault,
    DataHarvester=scripty.DataHarvesters.TreeDefault,
    TargetingFunction=scripty.TargetingFunctions.BoomkinDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Boomkin.StopCasting,
        scripty.Triggers.Boomkin.MoonfireExplosives,
        scripty.Triggers.Tree.SleepGhosts,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Tree.Renewal,
        scripty.Triggers.Tree.Barkskin,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Boomkin.SolarBeam,
        scripty.Triggers.LaserBear.NaturesVigil,
        scripty.Triggers.Boomkin.CelestialAlignment,
        scripty.Triggers.UseTrinket,
        scripty.Triggers.Boomkin.MoonkinForm2,
        scripty.Triggers.Boomkin.MightyBash,
        scripty.Triggers.Boomkin.RegrowthMe,
        scripty.Triggers.Boomkin.MightyBash,
        scripty.Triggers.LaserBear.Soothe,
        scripty.Triggers.Boomkin.RegrowthSave,
        scripty.Triggers.Boomkin.StarfallAvoidCap,
        scripty.Triggers.Boomkin.StarsurgeAvoidCap,
        scripty.Triggers.Boomkin.Sunfire,   
        scripty.Triggers.Boomkin.ConvokeTheSpirits,
        scripty.Triggers.LaserBear.Moonfire,
        scripty.Triggers.Boomkin.WrathForEclipse,
        scripty.Triggers.Boomkin.WarriorOfElune,
        scripty.Triggers.Boomkin.StarfireForEclipse,
        scripty.Triggers.LaserBear.HeartOfTheWild,
        scripty.Triggers.Boomkin.FuryOfElune,
        scripty.Triggers.Boomkin.Starfall,
        scripty.Triggers.Boomkin.Starsurge,
        scripty.Triggers.Boomkin.AstralCommunion,
        scripty.Triggers.Boomkin.WildMushroomAOE,
        scripty.Triggers.Boomkin.StellarFlare,
        scripty.Triggers.Boomkin.WildMushroom,
        scripty.Triggers.Boomkin.WarriorOfElune2,
        scripty.Triggers.Boomkin.Starfire,
        scripty.Triggers.Tree.Wrath,
        scripty.Triggers.LaserBear.StampedingRoar,
        -- scripty.Triggers.LaserBear.TigerDash,
        scripty.Triggers.LaserBear.MarkOfTheWild,
        scripty.Triggers.Oil,
        scripty.Triggers.Boomkin.RegrowthLow,
        scripty.Triggers.LaserBear.CatForm,
        scripty.Triggers.Boomkin.MoonkinForm,
        scripty.Triggers.Boomkin.Typhoon,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
        scripty.Triggers.Boomkin.UrsolsVortex,
        scripty.Triggers.Boomkin.EntanglingRoots,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Prot = {
    Name="Prot",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.ProtDefault,
    TargetingFunction=scripty.TargetingFunctions.ProtDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Prot.SpellReflection,
        scripty.Triggers.Prot.Charge,
        scripty.Triggers.HealthPotion,
        --scripty.Triggers.UseTrinket,
        --scripty.Triggers.UseTrinket2,
        scripty.Triggers.Prot.ShieldWall,
        scripty.Triggers.Prot.LastStand,
        scripty.Triggers.Prot.ShieldBlock,
        scripty.Triggers.Prot.ShieldBlock2,
        scripty.Triggers.Prot.IgnorePainOverFlow,
        scripty.Triggers.Prot.IgnorePain,
        scripty.Triggers.Prot.Pummel,
        --scripty.Triggers.Prot.ChallengingShout,
        scripty.Triggers.Prot.Taunt,
        scripty.Triggers.Prot.ImpendingVictory,
        scripty.Triggers.Prot.RallyingCry,
        scripty.Triggers.Prot.ThunderClapAoE,
        scripty.Triggers.Prot.Shockwave,
        scripty.Triggers.Prot.StormBolt,
        scripty.Triggers.Prot.StormBolt2,
        scripty.Triggers.Prot.Avatar,
        scripty.Triggers.Prot.DemoralizingShout,
        scripty.Triggers.Prot.ShieldCharge,
        scripty.Triggers.Prot.Ravager,
        scripty.Triggers.Prot.ThunderousRoar,
        scripty.Triggers.Prot.ShieldSlam,
        scripty.Triggers.Prot.ExecuteProc,
        scripty.Triggers.Prot.ThunderClap,
        scripty.Triggers.Prot.RevengeAoe,
        scripty.Triggers.Prot.RevengeProc,
        scripty.Triggers.Prot.Execute,
        --scripty.Triggers.Prot.Revenge,
        scripty.Triggers.Prot.RevengeLow,
        scripty.Triggers.Prot.WreckingThrow,
        scripty.Triggers.Prot.HeroicThrow,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Prot.BattleShout,
        scripty.Triggers.Prot.DefensiveStance,
        -- scripty.Triggers.Oil,
        -- scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Enhance = {
    Name="Enhance",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.RestoShammyDefault,
    TargetingFunction=scripty.TargetingFunctions.EnhanceDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.RestoShammy.FrostShockExplosives,
        scripty.Triggers.HealthPotion,
        -- scripty.Triggers.UseTrinket,
        --scripty.Triggers.UseTrinket2,
        scripty.Triggers.Enhance.SpiritWalk,
        scripty.Triggers.Enhance.SpiritWalk2,
        scripty.Triggers.RestoShammy.WindShear,
        scripty.Triggers.Enhance.AncestralGuidance,
        scripty.Triggers.Enhance.NaturesSwiftness,
        scripty.Triggers.Enhance.HealingSurgeMe,
        scripty.Triggers.Enhance.HealingSurgeSave,
        scripty.Triggers.Enhance.Sundering,
        scripty.Triggers.RestoShammy.Thunderstorm,
        scripty.Triggers.RestoShammy.LightningLasso,
        scripty.Triggers.Enhance.CapacitorTotem,
        scripty.Triggers.Enhance.GreaterPurge,
        scripty.Triggers.Enhance.FeralSpirit,
        scripty.Triggers.Enhance.LavaLashProc,
        scripty.Triggers.Enhance.LavaLashProc2,
        scripty.Triggers.Enhance.PrimordialWave,
        scripty.Triggers.RestoShammy.FlameShock,
        scripty.Triggers.Enhance.LightningBoltPrimordial,
        scripty.Triggers.Enhance.IceStrike,
        scripty.Triggers.Enhance.FrostShockHailstorm,
        scripty.Triggers.Enhance.ChainLightningMaelstromMax,
        scripty.Triggers.Enhance.LightningBoltMaelstromMax,
        scripty.Triggers.Enhance.CrashLightningBuff,
        scripty.Triggers.Enhance.StormstrikeCrash,
        scripty.Triggers.Enhance.CrashLightning,
        scripty.Triggers.Enhance.ChainLightningMaelstromMiddle,
        scripty.Triggers.Enhance.LightningBoltMaelstromMiddle,
        scripty.Triggers.Enhance.Stormstrike,
        scripty.Triggers.Enhance.LavaLash,
        scripty.Triggers.RestoShammy.HealingStreamTotem,
        scripty.Triggers.LootStuff,
        scripty.Triggers.RestoShammy.WindRushTotem,
        scripty.Triggers.RestoShammy.EarthShield,
        scripty.Triggers.Enhance.LightningShield,
        scripty.Triggers.Enhance.WindfuryWeapon,
        scripty.Triggers.Enhance.FlametongueWeapon,
        scripty.Triggers.RestoShammy.FrostShock,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.RestoShammy.GhostWolf,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.RestoShammy.GustOfWind,
        scripty.Triggers.RestoShammy.EarthbindTotem,
        scripty.Triggers.RestoShammy.EarthElemental,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Elemental = {
    Name="Elemental Shammy",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.RestoShammyDefault,
    TargetingFunction=scripty.TargetingFunctions.RestoShammyDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.RestoShammy.FrostShockExplosives,
        scripty.Triggers.RestoShammy.HexGhost,
        scripty.Triggers.HealthPotion,
        -- scripty.Triggers.UseTrinket,
        --scripty.Triggers.UseTrinket2,
        scripty.Triggers.RestoShammy.WindShear,
        scripty.Triggers.Elemental.SpiritwalkersGrace,
        scripty.Triggers.RestoShammy.PurifySpirit,
        -- scripty.Triggers.RestoShammy.HealingWave,
        scripty.Triggers.RestoShammy.HealingStreamTotem,
        -- scripty.Triggers.Enhance.GreaterPurge,
        scripty.Triggers.Elemental.HealingSurge,
        scripty.Triggers.Elemental.EarthquakeProc,
        scripty.Triggers.Elemental.EarthquakeCap,
        scripty.Triggers.Elemental.EarthShockCap,
        scripty.Triggers.Elemental.FireElemental,
        scripty.Triggers.Elemental.ChainLightningProc,
        scripty.Triggers.Elemental.LightningBoltProc,
        scripty.Triggers.Elemental.Stormkeeper,
        scripty.Triggers.Elemental.FlameShock,
        scripty.Triggers.RestoShammy.LavaBurstInstant,
        scripty.Triggers.Elemental.ChainLightning,
        scripty.Triggers.Elemental.LavaBurst,
        scripty.Triggers.Elemental.LightningBolt,
        scripty.Triggers.Elemental.Earthquake,
        scripty.Triggers.Elemental.EarthShock,
        scripty.Triggers.LootStuff,
        scripty.Triggers.RestoShammy.Skyfury,
        scripty.Triggers.RestoShammy.WindRushTotem,
        scripty.Triggers.RestoShammy.EarthShield,
        scripty.Triggers.Elemental.LightningShield,
        scripty.Triggers.RestoShammy.GhostWolf,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.RestoShammy.GustOfWind,
        scripty.Triggers.RestoShammy.EarthbindTotem,
        scripty.Triggers.RestoShammy.EarthElemental,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.RestoShammy = {
    Name="RestoShammy",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.RestoShammyDefault,
    TargetingFunction=scripty.TargetingFunctions.RestoShammyDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.RestoShammy.FrostShockExplosives,
        scripty.Triggers.RestoShammy.HexGhost,
        scripty.Triggers.RestoShammy.StopCasting,
        scripty.Triggers.HealthPotion,
        -- scripty.Triggers.UseTrinket,
        --scripty.Triggers.UseTrinket2,
        scripty.Triggers.RestoShammy.WindShear,
        scripty.Triggers.RestoShammy.AncestralGuidance,
        scripty.Triggers.RestoShammy.SpiritwalkersGrace,
        scripty.Triggers.RestoShammy.PurifySpirit,
        -- scripty.Triggers.RestoShammy.SpiritLinkTotem,
        -- scripty.Triggers.RestoShammy.Downpour,
        scripty.Triggers.RestoShammy.PrimordialWave,
        scripty.Triggers.RestoShammy.RiptideNoBuff,
        scripty.Triggers.RestoShammy.UnleashLife,
        scripty.Triggers.RestoShammy.ManaTideTotem,
        scripty.Triggers.RestoShammy.AncestralSwiftness,
        scripty.Triggers.RestoShammy.AncestralSwiftness2,
        scripty.Triggers.RestoShammy.HealingWavePrimordial,

        scripty.Triggers.RestoShammy.ChainHealInstant,
        scripty.Triggers.RestoShammy.HealingSurgeInstant,
        scripty.Triggers.RestoShammy.HealingSurgeInstant2,
        scripty.Triggers.RestoShammy.Ascendance,
        -- scripty.Triggers.RestoShammy.Thunderstorm,
        scripty.Triggers.RestoShammy.HealingTideTotem,
        -- scripty.Triggers.RestoShammy.Wellspring,
        -- scripty.Triggers.RestoShammy.UnleashLife,
        scripty.Triggers.RestoShammy.Riptide,
        scripty.Triggers.RestoShammy.HealingRainSingle,
        scripty.Triggers.RestoShammy.ChainHeal,
        scripty.Triggers.RestoShammy.HealingSurgeSave,
        -- scripty.Triggers.RestoShammy.ChainHealHighTide,
        -- scripty.Triggers.RestoShammy.ChainHealBingo,
        scripty.Triggers.RestoShammy.EarthShieldMissing,
        -- scripty.Triggers.RestoShammy.HealingWave,
        scripty.Triggers.RestoShammy.HealingStreamTotem,
        -- scripty.Triggers.Enhance.GreaterPurge,
        scripty.Triggers.RestoShammy.FlameShock,
        scripty.Triggers.RestoShammy.LavaBurstInstant,
        -- scripty.Triggers.RestoShammy.LightningLasso,
        scripty.Triggers.RestoShammy.ChainLightning,
        scripty.Triggers.RestoShammy.LavaBurst,
        scripty.Triggers.RestoShammy.LightningBolt,
        scripty.Triggers.RestoShammy.FrostShock,
        scripty.Triggers.LootStuff,
        scripty.Triggers.RestoShammy.Skyfury,
        scripty.Triggers.RestoShammy.WindRushTotem,
        scripty.Triggers.RestoShammy.EarthlivingWeapon,
        scripty.Triggers.RestoShammy.EarthShield,
        scripty.Triggers.RestoShammy.WaterShield,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.RestoShammy.GhostWolf,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.RestoShammy.GustOfWind,
        scripty.Triggers.RestoShammy.EarthbindTotem,
        scripty.Triggers.RestoShammy.EarthElemental,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.RestoShammyChainHeal = {
    Name="RestoShammy",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.RestoShammyDefault,
    TargetingFunction=scripty.TargetingFunctions.RestoShammyDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.RestoShammy.FrostShockExplosives,
        scripty.Triggers.RestoShammy.StopCasting,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.UseTrinket,
        --scripty.Triggers.UseTrinket2,
        scripty.Triggers.RestoShammy.WindShear,
        scripty.Triggers.RestoShammy.EverRisingTide,
        scripty.Triggers.RestoShammy.EverRisingTide2,
        scripty.Triggers.RestoShammy.AncestralGuidance,
        scripty.Triggers.RestoShammy.NaturesSwiftness,
        scripty.Triggers.RestoShammy.SpiritwalkersGrace,
        scripty.Triggers.RestoShammy.SpiritLinkTotem,
        scripty.Triggers.RestoShammy.Thunderstorm,
        scripty.Triggers.RestoShammy.HealingTideTotem,
        scripty.Triggers.RestoShammy.PurifySpirit,
        scripty.Triggers.RestoShammy.UnleashLife,
        scripty.Triggers.RestoShammy.HealingWavePrimordial,
        scripty.Triggers.RestoShammy.DownpourAoE,
        scripty.Triggers.RestoShammy.HealingRainAoE,
        scripty.Triggers.RestoShammy.ChainHealBingo,
        scripty.Triggers.RestoShammy.HealingSurgeTidalSave,
        scripty.Triggers.RestoShammy.PrimordialWave,
        scripty.Triggers.RestoShammy.RiptideNoTidal, 
        scripty.Triggers.RestoShammy.ChainHeal,
        scripty.Triggers.RestoShammy.EarthShieldMissing,
        scripty.Triggers.RestoShammy.ManaTideTotem,
        scripty.Triggers.RestoShammy.ManaSpringTotem,
        scripty.Triggers.RestoShammy.HealingStreamTotem,
        scripty.Triggers.RestoShammy.Riptide,
        scripty.Triggers.RestoShammy.HealingWave,
        scripty.Triggers.RestoShammy.FlameShock,
        scripty.Triggers.RestoShammy.LightningLasso,
        scripty.Triggers.RestoShammy.ChainLightning,
        scripty.Triggers.RestoShammy.LavaBurst,
        scripty.Triggers.RestoShammy.LightningBolt,
        scripty.Triggers.RestoShammy.FrostShock,
        scripty.Triggers.LootStuff,
        scripty.Triggers.RestoShammy.WindRushTotem,
        scripty.Triggers.RestoShammy.EarthlivingWeapon,
        scripty.Triggers.RestoShammy.EarthShield,
        scripty.Triggers.RestoShammy.WaterShield,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.RestoShammy.GhostWolf,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.RestoShammy.GustOfWind,
        scripty.Triggers.RestoShammy.EarthbindTotem,
        scripty.Triggers.RestoShammy.EarthElemental,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.ProtPala = {
    Name="ProtPala",
    EventHandler=scripty.EventHandlers.DiscDefault,
    DataHarvester=scripty.DataHarvesters.ProtPalaDefault,
    TargetingFunction=scripty.TargetingFunctions.ProtPalaDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.HolyPala.StopCasting,
        scripty.Triggers.Ret.RepentanceGhosts,
        scripty.Triggers.Prot.CleanseAfflicted,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.ProtPala.HammerOfWrathExplosives,
        scripty.Triggers.ProtPala.AvengersShieldExplosives,
        scripty.Triggers.ProtPala.JudgmentExplosives,
        scripty.Triggers.ProtPala.Rebuke,
        scripty.Triggers.ProtPala.Intercession,
        -- scripty.Triggers.ProtPala.ShieldOfTheRighteousDusk,
        scripty.Triggers.ProtPala.ShieldOfTheRighteous,
        scripty.Triggers.ProtPala.ShieldOfTheRighteous2,
        -- scripty.Triggers.ProtPala.ShieldOfTheRighteous3,
        scripty.Triggers.ProtPala.ShieldOfTheRighteousRedoubt,
        scripty.Triggers.ProtPala.ShieldOfTheRighteousMaintain,
        -- scripty.Triggers.ProtPala.ShieldOfTheRighteousBulwark,
        scripty.Triggers.ProtPala.HandOfReckoning,
        --scripty.Triggers.ProtPala.LayOnHandsMe,
        scripty.Triggers.ProtPala.AvengingWrath,
        scripty.Triggers.ProtPala.Sentinel,
        scripty.Triggers.ProtPala.MomentOfGlory,
        scripty.Triggers.ProtPala.GuardianOfAncientKings,
        scripty.Triggers.ProtPala.ArdentDefender,
        scripty.Triggers.ProtPala.LayOnHands,
        -- scripty.Triggers.ProtPala.BastionOfLight,
        -- scripty.Triggers.ProtPala.BlessingOfSacrifice,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.ProtPala.Consecration,
        --gcd saves
        scripty.Triggers.ProtPala.DivineShield,
        scripty.Triggers.ProtPala.WordOfGloryPlayerFirst,
        scripty.Triggers.ProtPala.WordOfGlory,
        --gcd interrupts
        scripty.Triggers.ProtPala.HolyBulwark,
        scripty.Triggers.ProtPala.EyeOfTyr,
        scripty.Triggers.ProtPala.AvengersShieldInterrupt,
        scripty.Triggers.ProtPala.BlindingLight,
        --gcd utility
        -- scripty.Triggers.ProtPala.BlessingOfFreedom,
        -- scripty.Triggers.ProtPala.BlessingOfProtection,
        -- scripty.Triggers.ProtPala.BlessingOfSpellwarding,
        scripty.Triggers.ProtPala.CleanseToxins,
        -- scripty.Triggers.ProtPala.AvengersShieldBulwark,
        scripty.Triggers.ProtPala.DivineToll,
        scripty.Triggers.ProtPala.AvengersShieldMoment,
        scripty.Triggers.ProtPala.HammerOfWrath2,
        scripty.Triggers.ProtPala.Judgment2,
        scripty.Triggers.ProtPala.BlessedHammer2,
        scripty.Triggers.ProtPala.HammerOfJustice,
        scripty.Triggers.ProtPala.AvengersShield,
        scripty.Triggers.ProtPala.HammerOfWrath,
        scripty.Triggers.ProtPala.Judgment,
        scripty.Triggers.ProtPala.BlessedHammer,
        scripty.Triggers.LootStuff,
        -- scripty.Triggers.ProtPala.ConsecrationLow,
        scripty.Triggers.ProtPala.DevotionAura,
        --scripty.Triggers.ProtPala.RetributionAura,
        -- scripty.Triggers.ProtPala.FlashOfLight,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.ProtPala.BlessedHammer3,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.ProtPala.DivineSteed,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Ret = {
    Name="Ret",
    EventHandler=scripty.EventHandlers.DiscDefault,
    DataHarvester=scripty.DataHarvesters.RetDefault,
    TargetingFunction=scripty.TargetingFunctions.HolyPalaDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.HolyPala.StopCasting,
        scripty.Triggers.Ret.RepentanceGhosts,
        scripty.Triggers.ProtPala.HammerOfWrathExplosives,
        scripty.Triggers.ProtPala.JudgmentExplosives,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.ProtPala.Intercession,
        scripty.Triggers.ProtPala.Rebuke,
        -- scripty.Triggers.Ret.AvengingWrath,
        scripty.Triggers.HolyPala.DivineProtection,
        scripty.Triggers.HolyPala.LayOnHands,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        --gcd saves
        scripty.Triggers.ProtPala.HammerOfJustice,
        scripty.Triggers.ProtPala.CleanseToxins,
        scripty.Triggers.ProtPala.BlessingOfFreedom,
        scripty.Triggers.Ret.ShieldOfVengeance,
        scripty.Triggers.Ret.FlashOfLightMyself,
        scripty.Triggers.Ret.WordOfGloryMyself,
        scripty.Triggers.Ret.FlashOfLightSave,
        -- scripty.Triggers.Ret.WordOfGlorySave,
        scripty.Triggers.Ret.DivineShield,
        scripty.Triggers.Ret.ExecutionSentence,
        scripty.Triggers.Ret.HammerOfWrath,
        scripty.Triggers.Ret.DivineStorm,
        scripty.Triggers.Ret.FinalVerdict,
        scripty.Triggers.Ret.TemplarSlash,
        scripty.Triggers.Ret.DivineToll,
        scripty.Triggers.Ret.WakeOfAshes,
        scripty.Triggers.Ret.BladeOfJustice,
        scripty.Triggers.Ret.CrusaderStrike,
        scripty.Triggers.ProtPala.Judgment,
        -- scripty.Triggers.Ret.WordOfGlory,
        scripty.Triggers.Ret.FlashOfLight,
        scripty.Triggers.LootStuff,
        scripty.Triggers.ProtPala.RetributionAura,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.ProtPala.DivineSteed,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.HolyPala = {
    Name="HolyPala",
    EventHandler=scripty.EventHandlers.DiscDefault,
    DataHarvester=scripty.DataHarvesters.HolyPalaDefault,
    TargetingFunction=scripty.TargetingFunctions.HolyPalaDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        --no gcd
        scripty.Triggers.HolyPala.StopCasting,
        scripty.Triggers.ProtPala.Intercession,
        scripty.Triggers.ProtPala.BlessingOfSacrifice,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.ManaPotion,
        scripty.Triggers.HolyPala.BeaconOfLight,
        scripty.Triggers.HolyPala.BeaconOfFaith,
        scripty.Triggers.ProtPala.Rebuke,
        scripty.Triggers.ProtPala.BlessingOfFreedom,
        scripty.Triggers.HolyPala.Cleanse,
        scripty.Triggers.HolyPala.AvengingWrath,
        scripty.Triggers.HolyPala.DivineProtection,
        scripty.Triggers.HolyPala.LayOnHands,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        --gcd saves
        scripty.Triggers.ProtPala.HammerOfJustice,
        scripty.Triggers.ProtPala.DivineShield,
        scripty.Triggers.HolyPala.HolyPrism,
        scripty.Triggers.HolyPala.WordOfGlory,
        scripty.Triggers.HolyPala.LightOfDawnAvoidCap,
        scripty.Triggers.ProtPala.ShieldOfTheRighteous,
        scripty.Triggers.HolyPala.DivineToll,
        scripty.Triggers.HolyPala.HolyShock,
        scripty.Triggers.HolyPala.HammerOfWrath,
        scripty.Triggers.HolyPala.Judgment,
        scripty.Triggers.HolyPala.CrusaderStrike,
        scripty.Triggers.HolyPala.HolyShockAttack,
        scripty.Triggers.HolyPala.HolyShockAttack2,
        scripty.Triggers.ProtPala.Consecration,
        scripty.Triggers.LootStuff,
        scripty.Triggers.ProtPala.DevotionAura,
        -- scripty.Triggers.Oil,
        -- scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        --dummy triggers
        scripty.Triggers.HolyPala.AuraMastery,
        scripty.Triggers.ProtPala.DivineSteed,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

--BM
Routines.BM = {
    Name="BM",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.BMDefault,
    TargetingFunction=scripty.TargetingFunctions.BMDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.BM.SingleSpec,
        scripty.Triggers.BM.AoeSpec,
        scripty.Triggers.BM.PvpSpec,
        scripty.Triggers.BM.ScareBeastGhosts,
        scripty.Triggers.Prepot,
        scripty.Triggers.AugmentRune,
        scripty.Triggers.Oil2,
        scripty.Triggers.HealthPotion,
        --scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.BM.Misdirection,
        scripty.Triggers.BM.HuntersMark,
        scripty.Triggers.BM.TranquilizingShot,
        scripty.Triggers.BM.CounterShot,
        scripty.Triggers.BM.Intimidation,
        scripty.Triggers.BM.ScatterShot,
        scripty.Triggers.Marks.Exhilaration,
        scripty.Triggers.BM.SummonPetOne,
        scripty.Triggers.BM.MendPet,
        scripty.Triggers.BM.RevivePet,
        scripty.Triggers.BM.BarbedShotPrimary,
        scripty.Triggers.BM.MultiShot,
        -- scripty.Triggers.BM.KillShotPrimary,
        scripty.Triggers.BM.KillShot,
        scripty.Triggers.BM.Bloodshed, 
        scripty.Triggers.BM.BestialWrath,
        scripty.Triggers.BM.KillCommand,
        scripty.Triggers.BM.DireBeast,
        scripty.Triggers.BM.BarbedShot,
        scripty.Triggers.BM.CobraShot,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Marks.Camouflage,
        scripty.Triggers.HunterTarget,
        scripty.Triggers.BM.FreezingTrap,
        scripty.Triggers.BM.ImplosiveTrap,
        scripty.Triggers.BM.BindingShot,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

--Marks
Routines.Marks = {
    Name="Marks",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.BMDefault,
    TargetingFunction=scripty.TargetingFunctions.MarksDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.BM.SingleSpec,
        scripty.Triggers.BM.AoeSpec,
        scripty.Triggers.BM.PvpSpec,
        scripty.Triggers.AugmentRune,
        scripty.Triggers.HealthPotion,
        --scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.BM.HuntersMark,
        scripty.Triggers.Marks.Trueshot,
        scripty.Triggers.Marks.CounterShot,
        scripty.Triggers.BM.ScatterShot,
        scripty.Triggers.BM.Intimidation,
        scripty.Triggers.Marks.Exhilaration,
        -- scripty.Triggers.Marks.AspectoftheTurtle,
        scripty.Triggers.Marks.MendPet,
        scripty.Triggers.Marks.KillShotProc,
        scripty.Triggers.Marks.SteadyShotForBuff,
        scripty.Triggers.Marks.AimedShotTrueshot,
        scripty.Triggers.Marks.RapidFire,
        scripty.Triggers.Marks.AimedShot,
        scripty.Triggers.Marks.KillShot,
        scripty.Triggers.Marks.ArcaneShot,
        scripty.Triggers.Marks.ExplosiveShot,
        scripty.Triggers.Marks.SniperShot,
        scripty.Triggers.Marks.SteadyShot,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Oil,
        scripty.Triggers.Marks.Camouflage,
        scripty.Triggers.Friendlies,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Marks.TarTrap,
        scripty.Triggers.BM.FreezingTrap,
        scripty.Triggers.BM.ImplosiveTrap,
        scripty.Triggers.BM.BindingShot,

    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Affliction = {
    Name="Affliction",
    EventHandler=scripty.EventHandlers.AfflictionDefault,
    DataHarvester=scripty.DataHarvesters.AfflictionDefault,
    TargetingFunction=scripty.TargetingFunctions.AfflictionDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Disc.StopCasting,
        scripty.Triggers.HealthPotion,
        --scripty.Triggers.UseTrinket,
        --bscripty.Triggers.UseTrinket2,
        scripty.Triggers.Flask,
        scripty.Triggers.Prepot,
        scripty.Triggers.Affliction.BurningRushCancel,
        scripty.Triggers.Affliction.BurningRushCancel2,
        scripty.Triggers.Affliction.DarkPact,
        scripty.Triggers.Affliction.MortalCoil,
        scripty.Triggers.Affliction.SpellLock,
        -- scripty.Triggers.Affliction.CurseOfExhaustion,
        scripty.Triggers.Affliction.SoulSwap,
        scripty.Triggers.Affliction.Soulrot,
        scripty.Triggers.Affliction.DrainLife,
        scripty.Triggers.Affliction.UnstableAffliction,
        scripty.Triggers.Affliction.Agony,  
        scripty.Triggers.Affliction.Corruption,
        scripty.Triggers.Affliction.AgonyRefresh,
        scripty.Triggers.Affliction.PhantomSingularity,
        scripty.Triggers.Affliction.Deathbolt,
        scripty.Triggers.Affliction.MaleficRaptureProc,
        scripty.Triggers.Affliction.MaleficRaptureCap,
        scripty.Triggers.Affliction.Haunt,
        scripty.Triggers.Affliction.DrainSoulProc,
        scripty.Triggers.Affliction.AgonyLow,
        --scripty.Triggers.Affliction.CorruptionLow,
        scripty.Triggers.Affliction.MaleficRapture,
        scripty.Triggers.Affliction.DrainSoul,
        scripty.Triggers.Affliction.GrimoireOfSacrifice,
        scripty.Triggers.Affliction.SummonFelhunter,
        scripty.Triggers.Affliction.BurningRush,
        scripty.Triggers.Affliction.SoulTap,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Oil,
    },
    ButtonList={}
}

Routines.Demo = {
    Name="Demo",
    EventHandler=scripty.EventHandlers.DemonologyDefault,
    DataHarvester=scripty.DataHarvesters.Default,
    TargetingFunction=scripty.TargetingFunctions.DemoDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Demo.StopCasting,
        scripty.Triggers.Prepot,
        scripty.Triggers.Affliction.BurningRushCancel,
        scripty.Triggers.Affliction.BurningRushCancel2,
        -- scripty.Triggers.Demo.FearSpiteful,
        scripty.Triggers.Demo.CommandDemon,
        scripty.Triggers.Demo.Implosion3,
        scripty.Triggers.Demo.FelDomination,
        scripty.Triggers.Demo.SummonFelguard,
        scripty.Triggers.Affliction.DarkPact,
        scripty.Triggers.Affliction.MortalCoil,
        scripty.Triggers.Demo.DrainLife,
        scripty.Triggers.Demo.HealthFunnel,
        scripty.Triggers.HealthPotion,
        -- scripty.Triggers.UseTrinket,
       -- scripty.Triggers.UseTrinket2,
        -- scripty.Triggers.Demo.AmplifyCurse,
        -- scripty.Triggers.Demo.CurseOfWeakness,
        -- scripty.Triggers.Demo.CurseOfExhaustion,
        scripty.Triggers.Demo.Shadowfury,
        scripty.Triggers.Affliction.Soulrot,
        scripty.Triggers.Demo.PowerSiphon,
        scripty.Triggers.Demo.NetherPortal,
        scripty.Triggers.Demo.GrimoireFelguard,
        scripty.Triggers.Demo.CallDreadstalkers,
        scripty.Triggers.Demo.HandOfGuldan,
        scripty.Triggers.Demo.SummonDemonicTyrant,
        scripty.Triggers.Demo.Implosion2,
        scripty.Triggers.Demo.Demonbolt,
        scripty.Triggers.Demo.DemonicStrength,
        scripty.Triggers.Demo.Implosion,
        scripty.Triggers.Demo.DrainLife2,
        scripty.Triggers.Demo.ShadowBolt,
        scripty.Triggers.Affliction.BurningRush,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Demo.PetAttack2,
        scripty.Triggers.Demo.PetAttack,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
    },
    ButtonList={}
}

Routines.Destro = {
    Name="Destro",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.Default,
    TargetingFunction=scripty.TargetingFunctions.DestroDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        -- scripty.Triggers.Affliction.BurningRushCancel,
        -- scripty.Triggers.Affliction.BurningRushCancel2,r
        scripty.Triggers.Destro.Fear,
        scripty.Triggers.Destro.Fear2,
        scripty.Triggers.Demo.FelDomination,
        scripty.Triggers.Affliction.SummonFelhunter,
        scripty.Triggers.Affliction.DarkPact,
        scripty.Triggers.Destro.DevourMagic,
        scripty.Triggers.Affliction.SpellLock,
        scripty.Triggers.HealthPotion,
        --scripty.Triggers.UseTrinket,
       -- scripty.Triggers.UseTrinket2,
        -- scripty.Triggers.Demo.AmplifyCurse,
        -- scripty.Triggers.Demo.CurseOfWeakness,
        scripty.Triggers.Demo.CurseOfExhaustion,
        scripty.Triggers.Demo.Shadowfury,
        scripty.Triggers.Affliction.MortalCoil,
        scripty.Triggers.Affliction.Soulrot,
        scripty.Triggers.Affliction.DrainLife,
        scripty.Triggers.Destro.ChaosBoltAvoidCap,
        scripty.Triggers.Destro.Immolate,
        scripty.Triggers.Destro.Conflagrate,
        scripty.Triggers.Destro.IncinerateAoe,
        scripty.Triggers.Destro.Incinerate,
        -- scripty.Triggers.Affliction.BurningRush,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Demo.PetAttack2,
        scripty.Triggers.Demo.PetAttack,
        scripty.Triggers.Affliction.InquisitorsGaze,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
    },
    ButtonList={}
}

Routines.Brew = {
    Name="Brew",
    EventHandler=scripty.EventHandlers.WWDefault,
    DataHarvester=scripty.DataHarvesters.BrewDefault,
    TargetingFunction=scripty.TargetingFunctions.BrewDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.WillToSurvive,
        scripty.Triggers.UseTrinket,
        scripty.Triggers.WW.TouchOfDeath,
        scripty.Triggers.Brew.Provoke,
        scripty.Triggers.Brew.CelestialBrew2,
        scripty.Triggers.Brew.CelestialBrew,
        scripty.Triggers.Brew.PurifyingBrew3,
        scripty.Triggers.Brew.PurifyingBrew2,
        scripty.Triggers.Brew.PurifyingBrew,
        scripty.Triggers.Brew.BlackOxBrew,
        scripty.Triggers.Brew.HealingElixir,
        scripty.Triggers.Brew.FortifyingBrew,
        scripty.Triggers.WW.SpearHandStrike,
        scripty.Triggers.Brew.Detox,
        scripty.Triggers.Brew.BlackOx,
        scripty.Triggers.Brew.DiffuseMagic,
        scripty.Triggers.Brew.DampenHarm,
        scripty.Triggers.Brew.BonedustBrew,
        scripty.Triggers.Brew.ExplodingKeg,
        scripty.Triggers.Brew.KegSmash,
        scripty.Triggers.WW.LegSweep,
        scripty.Triggers.Brew.ExpelHarm,
        scripty.Triggers.Brew.Paralysis,
        scripty.Triggers.Brew.ParalysisPvP,
        scripty.Triggers.Brew.BreathOfFirePvP,
        scripty.Triggers.WW.WeaponsOfOrder,
        scripty.Triggers.Brew.ExplodingKegSingle,
        scripty.Triggers.Brew.BreathOfFire,
        scripty.Triggers.Brew.RushingJadeWind,
        scripty.Triggers.Brew.RisingSunKick,
        scripty.Triggers.WW.WhiteTiger,
        scripty.Triggers.Brew.BlackoutKick,
        scripty.Triggers.Brew.SpinningCraneKick,        
        scripty.Triggers.Brew.TigerPalm,
        scripty.Triggers.Brew.ChiWave, 
        scripty.Triggers.Brew.SpinningCraneKickLow,        
        scripty.Triggers.Brew.RushingJadeWindLow,
        scripty.Triggers.Brew.VivifyMaintenance,
        scripty.Triggers.Brew.VivifyAnyone,
        scripty.Triggers.Brew.RingOfPeace,
        scripty.Triggers.WW.TigersLust,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        --scripty.Triggers.Brew.Transcendence,
        scripty.Triggers.Friendlies,
    },
    ButtonList={}
}


Routines.WW = {
    Name="WW",
    EventHandler=scripty.EventHandlers.WWDefault,
    DataHarvester=scripty.DataHarvesters.Default,
    TargetingFunction=scripty.TargetingFunctions.WWDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.WW.TouchOfDeath,
        scripty.Triggers.UseTrinket,
        scripty.Triggers.WW.SpearHandStrike,
        scripty.Triggers.WW.TouchOfKarma,
        scripty.Triggers.WW.DiffuseMagic,
        scripty.Triggers.WW.DampenHarm,
        scripty.Triggers.WW.LegSweep,
        scripty.Triggers.WW.DetoxMe,
        scripty.Triggers.WW.ExpelHarm,
        scripty.Triggers.WW.TigerPalm,
        -- scripty.Triggers.WW.Transcendence,
        -- scripty.Triggers.WW.TranscendenceTransfer,
        scripty.Triggers.WW.VivifyMe,
        scripty.Triggers.WW.WeaponsOfOrder,
        scripty.Triggers.WW.Xuen,
        scripty.Triggers.WW.StormEarthandFire,
        scripty.Triggers.WW.StormEarthandFire2,
        scripty.Triggers.WW.WhiteTiger,
        scripty.Triggers.WW.StrikeOfTheWindlord,
        scripty.Triggers.WW.RisingSunKick,
        scripty.Triggers.WW.FistsOfFury,
        scripty.Triggers.WW.SpinningCraneKickProc,
        scripty.Triggers.WW.SpinningCraneKick,
        scripty.Triggers.WW.BlackoutKick,
        scripty.Triggers.WW.RushingJadeWind,        
        scripty.Triggers.WW.ChiWave, 
        -- scripty.Triggers.WW.VivifyRun,
        scripty.Triggers.WW.TigersLust,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={}
}

Routines.Mist = {
    Name="Mist",
    EventHandler=scripty.EventHandlers.WWDefault,
    DataHarvester=scripty.DataHarvesters.MistDefault,
    TargetingFunction=scripty.TargetingFunctions.WWDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Mist.CancelNoggenFogger,
        scripty.Triggers.Mist.StopCasting,
        scripty.Triggers.Mist.ParaGhosts,
        -- scripty.Triggers.UseTrinket,
        scripty.Triggers.Prepot,
        scripty.Triggers.Mist.LifeCocoon,
        scripty.Triggers.Mist.ThunderFocusTea,
        scripty.Triggers.WW.TouchOfDeath,
        scripty.Triggers.WW.SpearHandStrike,
        scripty.Triggers.Mist.DetoxAfflicted,
        scripty.Triggers.WW.LegSweep,
        scripty.Triggers.Brew.Detox,
        scripty.Triggers.Mist.InvokeChijitheRedCrane,
        scripty.Triggers.Mist.EnvelopingMistInstantCast2,
        scripty.Triggers.Mist.EnvelopingMistInstantCast3,
        scripty.Triggers.Mist.EnvelopingMistInstantCast4,
        scripty.Triggers.Mist.Revival,
        scripty.Triggers.Mist.ExpelHarm,
        scripty.Triggers.Mist.VivifyInstant,
        scripty.Triggers.Mist.SpinningCraneKickProc,
        scripty.Triggers.Mist.ManaTea2,
        scripty.Triggers.Mist.RisingSunKick,
        scripty.Triggers.Mist.RenewingMist,
        scripty.Triggers.Mist.Vivify,
        scripty.Triggers.Mist.JadefireStomp,
        scripty.Triggers.Mist.JadefireStomp2,
        scripty.Triggers.Mist.SpinningCraneKickAoe,
        scripty.Triggers.Mist.BlackoutKick,
        -- scripty.Triggers.Mist.BlackoutKick2,
        scripty.Triggers.Mist.TigerPalm,
        scripty.Triggers.Mist.ManaTea,
        scripty.Triggers.WW.TigersLust,
        scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={}
}

Routines.Fire = {
    Name="Fire",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.ArcaneDefault,
    TargetingFunction=scripty.TargetingFunctions.FireDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        -- scripty.Triggers.Fire.StopCasting,
        scripty.Triggers.Fire.FireBlastCritConfirm,
        scripty.Triggers.Fire.FireBlastAvoidCap,
        scripty.Triggers.Fire.FleshcraftLow,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Arcane.Counterspell,
        scripty.Triggers.Fire.IceFloes,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.Fire.Deathborne,
        scripty.Triggers.Fire.Combustion,
        scripty.Triggers.Fire.PyroblastProc, 
        scripty.Triggers.Fire.Spellsteal,
        scripty.Triggers.Arcane.FrostNova,
        scripty.Triggers.Arcane.BlastWave,
        scripty.Triggers.Fire.IceNova,
        scripty.Triggers.Fire.BlazingBarrier2,
        scripty.Triggers.Arcane.Slow,
        scripty.Triggers.Arcane.RemoveCurse,
        scripty.Triggers.Arcane.MirrorImage,
        scripty.Triggers.Fire.ScorchExecute,
        scripty.Triggers.Fire.Pyroblast, 
        scripty.Triggers.Fire.ShiftingPower,
        scripty.Triggers.Fire.PhoenixFlames,
        scripty.Triggers.Fire.LivingBomb, 
        scripty.Triggers.Arcane.ConeOfCold,      
        scripty.Triggers.Fire.Scorch,
        scripty.Triggers.Fire.Frostbolt,
       -- scripty.Triggers.Arcane.Polymorph,
        scripty.Triggers.Arcane.ArcaneExplosionLowPriority,
        scripty.Triggers.Arcane.ArcaneIntellect,
        scripty.Triggers.Fire.BlazingBarrier,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Oil,
        scripty.Triggers.Flask,
        scripty.Triggers.Disc.Fleshcraft,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
        scripty.Triggers.Arcane.IceBlock,
        scripty.Triggers.Arcane.Blink,
        scripty.Triggers.Arcane.GreaterInvisibility,
        scripty.Triggers.Arcane.AlterTime,

        --scripty.Triggers.LootStuff,
        scripty.Triggers.AcquireTarget,
        
    },
    ButtonList={}
}

Routines.Holy = {
    Name="Holy",
    EventHandler=scripty.EventHandlers.DiscDefault,
    DataHarvester=scripty.DataHarvesters.HolyDefault,
    TargetingFunction=scripty.TargetingFunctions.DiscDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Disc.Purify,
        scripty.Triggers.Holy.StopCasting,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.AugmentRune,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        -- scripty.Triggers.Flask,
        -- scripty.Triggers.Prepot,
        -- scripty.Triggers.Holy.Premonition,
        scripty.Triggers.Disc.PowerInfusion,
        scripty.Triggers.Disc.Fade,
        scripty.Triggers.Holy.GuardianSpirit,
        scripty.Triggers.Shadow.PsychicScream,
        scripty.Triggers.Shadow.DominateGhosts,
        scripty.Triggers.Shadow.ShackleGhosts,
        scripty.Triggers.Holy.DivineHymn,
        scripty.Triggers.Holy.PowerWordLife,
        scripty.Triggers.Holy.FlashHealResonant,
        scripty.Triggers.Disc.FlashHealProc,
        scripty.Triggers.Holy.HolyWordSerenity,
        scripty.Triggers.Holy.HolyWordSerenity2,
        scripty.Triggers.Holy.Lightwell,
        scripty.Triggers.Holy.HolyWordSanctify,
        scripty.Triggers.Holy.HolyWordChastiseHeal,
        scripty.Triggers.Holy.Apotheosis,
        scripty.Triggers.Disc.PrayerOfMending,
        scripty.Triggers.Holy.FlashHeal,
        scripty.Triggers.Holy.HolyFire, 
        scripty.Triggers.Disc.ShadowWordDeath,
        scripty.Triggers.Holy.HolyWordChastise,
        scripty.Triggers.Disc.Smite,
        scripty.Triggers.Holy.Mindbender,
        scripty.Triggers.Holy.ShadowWordPain,
        scripty.Triggers.Holy.PowerWordShieldPrep,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Holy.Renew,
        scripty.Triggers.Disc.PowerWordShieldLowPriority,
        scripty.Triggers.Holy.RenewLowPriority,
        scripty.Triggers.Disc.PowerWordFortitude,
        -- scripty.Triggers.Disc.PowerWordShieldSpeed,
        scripty.Triggers.Disc.Feather,
        scripty.Triggers.Oil,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}


Routines.Shadow = {
    Name="Shadow",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.HolyDefault,
    TargetingFunction=scripty.TargetingFunctions.ShadowDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Disc.PowerWordLife,
        scripty.Triggers.Shadow.DominateGhosts,
        scripty.Triggers.Shadow.ShackleGhosts,
        scripty.Triggers.Shadow.Dispersion,
        scripty.Triggers.Shadow.Shadowform,
        scripty.Triggers.Shadow.Silence,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.AugmentRune,
        scripty.Triggers.Prepot,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.Flask,
        scripty.Triggers.Shadow.PowerWordShieldMe,
        scripty.Triggers.Disc.FlashHealProc,
        scripty.Triggers.Disc.PowerInfusion,
        scripty.Triggers.Shadow.PowerWordShieldSave,
        scripty.Triggers.Disc.Fade,
        scripty.Triggers.Shadow.VampiricEmbrace,
        scripty.Triggers.Shadow.PsychicScream,
        scripty.Triggers.Disc.VoidTendrils,
        scripty.Triggers.Shadow.ShadowWordDeath,
        scripty.Triggers.Shadow.ShadowWordDeath2,
        scripty.Triggers.Shadow.PowerWordShield,
        scripty.Triggers.Shadow.DevouringPlague,
        scripty.Triggers.Shadow.VampiricTouch,
        scripty.Triggers.Disc.Mindbender,
        scripty.Triggers.Shadow.VoidEruption,
        scripty.Triggers.Shadow.VoidBolt,
        scripty.Triggers.Shadow.DarkAscension,
        scripty.Triggers.Shadow.VoidTorrent,
        scripty.Triggers.Shadow.MindBlast,
        scripty.Triggers.Shadow.MindBlast2,
        scripty.Triggers.Disc.Mindgames,
        scripty.Triggers.Disc.UnholyNova,
        scripty.Triggers.Disc.Halo,
        scripty.Triggers.Disc.PowerWordShieldSpeed,
        scripty.Triggers.Shadow.MindSpike,
        scripty.Triggers.Disc.HolyNova,
        scripty.Triggers.Disc.HolyNovaProc,
        scripty.Triggers.Disc.PrayerOfMending,
        scripty.Triggers.Disc.PowerWordShield,
        scripty.Triggers.Disc.FlashHeal,
        scripty.Triggers.Shadow.ShadowWordPain,
        scripty.Triggers.Disc.RenewLowPriority,
        scripty.Triggers.Disc.PowerWordFortitude,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Disc.Feather,
        scripty.Triggers.Disc.Fleshcraft,
        scripty.Triggers.Oil,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

Routines.Disc = {
    Name="Disc",
    EventHandler=scripty.EventHandlers.DiscDefault,
    DataHarvester=scripty.DataHarvesters.DiscDefault,
    TargetingFunction=scripty.TargetingFunctions.DiscDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        scripty.Triggers.Disc.PurgeExplosives,
        scripty.Triggers.Disc.StopCasting,
        scripty.Triggers.Shadow.DominateGhosts,
        scripty.Triggers.Shadow.ShackleGhosts,
        scripty.Triggers.HealthPotion,
        scripty.Triggers.Prepot,
        scripty.Triggers.AugmentRune,
        -- scripty.Triggers.UseTrinket,
        scripty.Triggers.UsePriestTrinket2,
        -- scripty.Triggers.Flask,
        scripty.Triggers.Disc.DesperatePrayer,
        scripty.Triggers.Disc.PowerInfusion,
        scripty.Triggers.Disc.Fade,
        scripty.Triggers.Disc.LuminousBarrier,
        scripty.Triggers.Disc.LuminousBarrier2,
        scripty.Triggers.Disc.PainSuppression,
        scripty.Triggers.Disc.PainSuppression2,
        scripty.Triggers.Disc.RadianceOnTime,
        scripty.Triggers.Disc.PowerWordLife,
        scripty.Triggers.Disc.Purify,
        scripty.Triggers.Disc.RadianceForAtonement,
        scripty.Triggers.Disc.RadianceForHealth,
        scripty.Triggers.Disc.PowerWordShield,
        scripty.Triggers.Disc.Rapture,  
        scripty.Triggers.Disc.FlashHeal,
        scripty.Triggers.Disc.FlashHealProc2,
        scripty.Triggers.Disc.PowerWordShieldPrep,
        scripty.Triggers.Disc.PowerWordShieldPrep2,
        scripty.Triggers.Disc.RapturePrep,
        scripty.Triggers.Disc.PrayerOfMending,
        scripty.Triggers.Disc.Mindbender,
        scripty.Triggers.Disc.Mindbender2,
        scripty.Triggers.Disc.RenewPrep,
        scripty.Triggers.Disc.Mindgames,
        scripty.Triggers.Disc.MindBlast,
        scripty.Triggers.Disc.Penance,
        scripty.Triggers.Disc.ShadowWordDeath,
        scripty.Triggers.Disc.UltimatePenitence,
        -- scripty.Triggers.Disc.FlashHeal2,
        scripty.Triggers.Disc.PurgeTheWicked,
        scripty.Triggers.Disc.Smite,
        scripty.Triggers.Disc.FlashHealProc,
        scripty.Triggers.Disc.PenanceHeal,  
        scripty.Triggers.Disc.Halo,
        -- scripty.Triggers.Disc.HolyNova,
        -- scripty.Triggers.Disc.HolyNovaProc,
        scripty.Triggers.Disc.PenanceHealLowPriority,
        scripty.Triggers.Disc.PowerWordFortitude,
        scripty.Triggers.LootStuff,
        scripty.Triggers.Disc.Renew,
        scripty.Triggers.Disc.Feather,
        scripty.Triggers.Oil,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Lethal",
            Property="forceAttackMode" 
        },
    }
}

scripty.Data.tauntEnabled = true
Routines.BloodDK = {
    Name="Blood DK",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.BloodDKDefault,
    TargetingFunction=scripty.TargetingFunctions.BloodDKDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    Bindings=scripty.Bindings.BloodDK,
    TriggerList={
        scripty.Triggers.BloodDK.RaiseDead,
        scripty.Triggers.BloodDK.Marrowrend1,
        scripty.Triggers.BloodDK.Marrowrend2,
        scripty.Triggers.BloodDK.EmpowerRuneWeapon,
        scripty.Triggers.BloodDK.DancingRuneWeapon,
        scripty.Triggers.BloodDK.Tombstone,
        scripty.Triggers.BloodDK.HeartStrike1,
        scripty.Triggers.BloodDK.HeartStrike2,
        scripty.Triggers.BloodDK.HeartStrike3,
        scripty.Triggers.BloodDK.DeathsCaress1,
        scripty.Triggers.BloodDK.DeathsCaress2,
        scripty.Triggers.BloodDK.BloodTap,
        scripty.Triggers.BloodDK.DeathAndDecay1,
        scripty.Triggers.BloodDK.DeathAndDecay2,
        scripty.Triggers.BloodDK.BloodBoil1,
        scripty.Triggers.BloodDK.BloodBoil2,
        scripty.Triggers.BloodDK.VampiricBlood,
        scripty.Triggers.BloodDK.DarkCommand,
        --scripty.Triggers.BloodDK.RuneTap1,
        --scripty.Triggers.BloodDK.RuneTap2,
        --scripty.Triggers.BloodDK.Bonestorm,
    },
    ButtonList={
        {
            Text="Taunt",
            Property="tauntEnabled" -- i.e. 'scripty.Data.tauntEnabled'
        },
    }
}

Routines.SophieDK = {
    Name="Sophie DK",
    EventHandler=scripty.EventHandlers.Default,
    DataHarvester=scripty.DataHarvesters.SophieDKDefault,
    TargetingFunction=scripty.TargetingFunctions.SophieDKDefault,
    ForceAttackFunction=scripty.ForceAttackFunctions.Default,
    PriorityUnitFunction=scripty.PriorityFunctions.Default,
    TriggerList={
        -- scripty.Triggers.HealthPotion,
        -- scripty.Triggers.UseTrinket,
        -- scripty.Triggers.UseTrinket2,
        scripty.Triggers.SophieDK.DeathPact,
        scripty.Triggers.SophieDK.DeathStrike2,
        scripty.Triggers.SophieDK.BlindingSleet,
        scripty.Triggers.SophieDK.DeathGrip,
        -- scripty.Triggers.SophieDK.DeathGrip2,
        scripty.Triggers.SophieDK.MindFreeze,
        scripty.Triggers.SophieDK.VampiricBlood,
        scripty.Triggers.SophieDK.VampiricBlood2,
        scripty.Triggers.SophieDK.DancingRuneWeapon,
        scripty.Triggers.SophieDK.EmpowerRuneWeapon,
        scripty.Triggers.SophieDK.Bonestorm,
        scripty.Triggers.SophieDK.RuneTap,
        scripty.Triggers.SophieDK.AntiMagicShell,
        scripty.Triggers.SophieDK.BloodTap,
        scripty.Triggers.SophieDK.DarkCommand,
        scripty.Triggers.SophieDK.Marrowrend2,
        scripty.Triggers.SophieDK.Asphyxiate,
        scripty.Triggers.SophieDK.ChainsOfIce,
        scripty.Triggers.SophieDK.BloodBoil,
        scripty.Triggers.SophieDK.DeathAndDecay,
        scripty.Triggers.SophieDK.DeathStrike,
        scripty.Triggers.SophieDK.Tombstone,
        scripty.Triggers.SophieDK.AbominationLimb,
        scripty.Triggers.SophieDK.RaiseDead,
        scripty.Triggers.SophieDK.DeathsCaress,
        scripty.Triggers.SophieDK.Marrowrend,
        scripty.Triggers.SophieDK.HeartStrike,
        scripty.Triggers.SophieDK.SoulReaper,
        scripty.Triggers.SophieDK.Blooddrinker,
        scripty.Triggers.SophieDK.BloodBoil2,
        scripty.Triggers.SophieDK.WraithWalk,
        -- scripty.Triggers.LootStuff,
        -- scripty.Triggers.Oil,
        -- scripty.Triggers.Flask,
        scripty.Triggers.AcquireTarget,
        scripty.Triggers.Friendlies,
    },
    ButtonList={
        {
            Text="Taunt",
            Property="tauntEnabled" -- i.e. 'scripty.Data.tauntEnabled'
        },
    }
}

scripty.Routines = Routines