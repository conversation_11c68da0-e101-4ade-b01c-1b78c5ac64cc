local addonName, scripty = ...

local Conditions = {}
local auraCache = {}

local function IDPredicate(auraIDToFind,_,_, _,_,_,_,_,_,_,_,_,spellID)
    return auraIDToFind == spellID
end

function GetAuraInfo(unit, aura, filter)
    if not filter then
        print("forgot filter for " .. aura )
    end
    if not unit then
        return nil
    end
    local auraString = tostring(aura)
    if auraCache[unit .. aura .. filter] and GetTime() - auraCache[unit .. auraString .. filter].time < 0.2 then
        return auraCache[unit .. auraString .. filter]
    end
    local name, icon, count, dispelType, totalDuration, expirationTime, source, isStealable, nameplateShowPersonal, spellId, canApplyAura, isBossDebuff, castByPlayer
    if type(aura) == "number" then
        name, icon, count, dispelType, totalDuration, expirationTime, source, isStealable, nameplateShowPersonal, spellId, canApplyAura, isBossDebuff, castByPlayer = AuraUtil.FindAura(IDPredicate, unit, filter, aura)
    else
        name, icon, count, dispelType, totalDuration, expirationTime, source, isStealable, nameplateShowPersonal, spellId, canApplyAura, isBossDebuff, castByPlayer = AuraUtil.FindAuraByName(aura, unit, filter)
    end
    local auraInfo = 
    {
        name = name, 
        icon = icon, 
        count = count, 
        dispelType = dispelType, 
        totalDuration = totalDuration, 
        expirationTime = expirationTime, 
        source = source, 
        isStealable = isStealable, 
        nameplateShowPersonal = nameplateShowPersonal, 
        spellId = spellId, 
        canApplyAura = canApplyAura, 
        isBossDebuff = isBossDebuff, 
        castByPlayer = castByPlayer,
        time = GetTime(),
    }
    auraCache[unit .. aura .. filter] = auraInfo
    return auraInfo
end

function IsAnAoERaidBoss(bossName)
    if bossName == "Queen Ansurek" then
        return true
    end
    if bossName == "Anub'Arash" then
        return true
    end
    if bossName == "Skeinspinner Takazj" then
        return true
    end
    if bossName == "Ulgrax the Devourer" then
        return true
    end
    if bossName == "Moira Bronzebeard" then
        return true
    end
    if bossName == "Emperor Dagran Thaurissan" then
        return true
    end
    return false
end

Conditions.IsFlyableAreaForPlayer = function()
    return function()
        return IsFlyableArea()
    end
end

Conditions.PlayerIsFlying = function()
    return function()
        return IsFlying()
    end
end

Conditions.PlayerIsIndoors = function()
    return function()
        return not IsOutdoors()
    end
end

--Targetting
Conditions.TargetIsHighestPriority = function()
    return function()
        local highestPriority = scripty.Data.highestPriorityAround
        return scripty.Data.currentRoutine.TargetingFunction("target") >= highestPriority
    end
end
--Casting/Channel
Conditions.PlayerIsChannelingAbility = function(ability)
    return function()
        return UnitChannelInfo("player") == ability
    end
end
Conditions.PlayerIsChannelingAnything = function(ability)
    return function()
        if UnitChannelInfo("player") then
            return true
        end
        return false
    end
end
Conditions.PlayerIsCastingAnything = function(ability)
    return function()
        if UnitCastingInfo("player") then
            return true
        end
        return false
    end
end
Conditions.PlayerIsCastingAbility = function(ability)
    return function()
        return UnitCastingInfo("player") == ability
    end
end
Conditions.PlayerIsDoingSomething = function()
    return function()
        return (UnitCastingInfo("player") or UnitChannelInfo("player"))
    end
end
Conditions.IncomingAOEDamage = function()
    return function()
        return scripty.Data.IncomingMultiTargetDamage
    end
end

Conditions.SecondsUntilAOELessThan = function(seconds)
    return function()
        return (scripty.Data.timeUntilAOE > 0 and scripty.Data.timeUntilAOE < seconds)
    end
end

Conditions.DamagingDebuffsGreaterThan = function(amount)
    return function()
        return scripty.Data.numberOfDamagingDebuffs > amount
    end
end

Conditions.PlayerIsThreatened = function()
    return function()
        status = UnitThreatSituation("player")
        return (status and status > 0)
    end
end

Conditions.TotemIsUp = function()
    return function()
        return GetTotemInfo(1)
    end
end
--Crowd Control
Conditions.UnitIsImmuneToAbility = function(unit, ability)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local immuneTime = nil
        if scripty.Data.ccImmunes and scripty.Data.ccImmunes[ability] then
            immuneTime = scripty.Data.ccImmunes[ability][UnitGUID(extractedUnit)]
        end
        if immuneTime then
            return true
        end
    end
end
--Items
Conditions.ItemIsReady = function(itemID)
    return function()
        local startTime, duration, enable = GetItemCooldown(itemID)
        local count = GetItemCount(itemID)
        return (enable and duration == 0 and count > 0)
    end
end
Conditions.TrinketIsReady = function(trinketNumber)
    return function()
        local itemID = nil
        if trinketNumber == 2 then
            itemID = GetInventoryItemID("player", "TRINKET1SLOT")
        else
            itemID = GetInventoryItemID("player", "TRINKET0SLOT")
        end
        if itemID == nil then
            return false
        end
        local startTime, duration, enable = GetItemCooldown(itemID)
        local count = GetItemCount(itemID)
        return (enable and duration == 0 and count > 0)
    end
end
Conditions.StaffIsReady = function()
    return function()
        local itemID = nil
        itemID = GetInventoryItemID("player", 16)
        if itemID == nil then
            return false
        end
        local startTime, duration, enable = GetItemCooldown(itemID)
        return (enable and duration == 0)
    end
end
Conditions.ToyOffCD = function(itemID)
    return function()
        local  startTime, duration, enable = GetItemCooldown(itemID)
        return (enable and duration == 0)
    end
end
Conditions.ItemCountLessThan = function(itemID, amount)
    return function()
        local count = GetItemCount(itemID)
        return count < amount
    end
end

local function isWearingSet(setID)
    if not setID then return false end

    local slotIDs = C_EquipmentSet.GetItemIDs(setID) -- Gets the item IDs for each slot in the set
    for slot, itemID in pairs(slotIDs) do
        if itemID then
            local equippedItemID = GetInventoryItemID("player", slot)
            if equippedItemID ~= itemID then
                return false -- If any item slot doesn’t match, return false
            end
        end
    end
    return true -- All items match
end

Conditions.SpecMismatch = function()
    return function()
        return scripty.Data.lastSpecializationMismatchTime and GetTime() - scripty.Data.lastSpecializationMismatchTime < 2
    end
end

Conditions.SpecAndSetShouldBe = function(setName)
    return function()
        local inWarmode = C_PvP.IsWarModeDesired()
        local inInstance, instanceType = IsInInstance()
        local setThatShouldBeEquipped = "single"
        if (inWarmode and not inInstance) or (instanceType == "pvp" or instanceType == "arena") then
            setThatShouldBeEquipped = "pvp"
        else
            if IsInInstance() and instanceType ~= "raid" then
                setThatShouldBeEquipped = "aoe"
            end
        end
        if IsAnAoERaidBoss(UnitName("target")) and not UnitIsDeadOrGhost("target") then
            setThatShouldBeEquipped = "aoe"
            scripty.Data.aoeBossTimer = GetTime()
        end
        if IsInInstance() and instanceType == "raid" and scripty.Data.aoeBossTimer and GetTime() - scripty.Data.aoeBossTimer < 300 then
            setThatShouldBeEquipped = "aoe"
        end
        if setThatShouldBeEquipped ~= setName then
            return false
        end
        local setID = C_EquipmentSet.GetEquipmentSetID(setThatShouldBeEquipped)
		if setID then
			local isEquipped = isWearingSet(setID)
            if not isEquipped then
                return true
            end
        end
        local name = C_Traits.GetConfigInfo(C_ClassTalents.GetLastSelectedSavedConfigID(PlayerUtil.GetCurrentSpecID())).name
        if name and name ~= setName then
            return true
        end
        return false
    end
end

--Resources
Conditions.AbilityHasResources = function(ability)
    return function()
        local usable, noMana = C_Spell.IsSpellUsable(ability)
        --hacks
        if ability == "Word of Glory" then
            local auraInfo = GetAuraInfo("player", "Shining Light", "HELPFUL")
            if auraInfo.spellId == 327510 then
                return true
            end
        end
        return not noMana
    end
end
Conditions.PlayerManaGreaterThanHPOfTarget = function(unit, modifier)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if UnitExists(extractedUnit) then
            if hitpoints(extractedUnit) < 35 then
                return true
            end
            if hitpoints(extractedUnit) > 90 then
                return false
            end
            return (UnitPower("player", SOPHIE_POWER_MANA) / UnitPowerMax("player", SOPHIE_POWER_MANA) * 100) >= hitpoints(extractedUnit) - modifier
        end
        return true
    end
end
Conditions.PlayerResourceMinimum = function(type, amount)
    return function()
        if type == SOPHIE_POWER_MANA then
            if Conditions.UnitHasAura("player", "Innervate", "HELPFUL")() then
                return true
            end
            if Conditions.UnitHasAura("player", "Potion of Chilled Clarity", "HELPFUL")() then
                return true
            end
            return UnitPower("player", type) / UnitPowerMax("player", type) * 100 >= amount
        end
        return UnitPower("player", type) >= amount
    end
end
Conditions.PlayerResourceMinimumAdjusted = function(type, amount)
    return function()
        local resourceAmount = UnitPower("player", type)
        if type == SOPHIE_POWER_SOULSHARDS then
            local spell = UnitCastingInfo("player")
            if spell == "Hand of Gul'dan" then
                resourceAmount = resourceAmount - 3
            end
            if spell == "Summon Vilefiend" then
                resourceAmount = resourceAmount - 1
            end
            if spell == "Call Dreadstalkers" then
                resourceAmount = resourceAmount - 2
            end
            if spell == "Shadow Bolt" then
                resourceAmount = resourceAmount + 1
            end
            if spell == "Summon Demonic Tyrant" then
                resourceAmount = 5
            end
            if resourceAmount < 0 then
                resourceAmount = 0
            end
        end
        return resourceAmount >= amount
    end
end
Conditions.SpellChargesGreaterThan = function(spell, charges)
    return function()
        local testCharges = C_Spell.GetSpellCharges(spell)
        if not testCharges then
            return false
        end
        local spellCharges = testCharges["currentCharges"] or -1
        return spellCharges > charges
    end
end

Conditions.StunnedRecently = function()
    return function()
        if scripty.Data.lastStunnedTime then
            return GetTime() - scripty.Data.lastStunnedTime < 1 
        end
        return false
    end
end
--Range
Conditions.AbilityInRange = function(ability, unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if UnitIsUnit(extractedUnit, "player") then
            return true
        end
        return C_Spell.IsSpellInRange(ability, extractedUnit)
    end
end
--Movement
Conditions.PlayerMoving = function()
    return function()
        return IsPlayerMoving()
    end
end
Conditions.UnitIsMoving = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        return GetUnitSpeed(extractedUnit) > 4
    end
end
Conditions.PlayerBeenMoving = function()
    return function()
        return scripty.Data.sophieBeenMoving or false
    end
end

Conditions.PlayerStandingStill = function()
    return function()
        return scripty.Data.sophieStandinStill or false
    end
end

Conditions.UnitHPLessThanUnit = function(unit, unit2, difference)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local extractedUnit2 = scripty.Data.UnitFromUnit(unit2)
        if not extractedUnit or not extractedUnit2 then
            return false
        end
        if not extractedUnit == "no one" or extractedUnit2 == "no one" then
            return false
        end
        if UnitIsUnit(extractedUnit, extractedUnit2) then
            return false
        end
        local hp = UnitHealth(extractedUnit) / UnitHealthMax(extractedUnit) * 100
        local hp2 = UnitHealth(extractedUnit2) / UnitHealthMax(extractedUnit2) * 100
        return hp < hp2 - difference
    end
end

Conditions.UnitHPDamageAtLeastTakenSince = function(unit, hp, maximumTime)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local damageTable = scripty.Data.damageTable
        if not damageTable then
            return false
        end
        local unitDamageTable = scripty.Data.damageTable[UnitGUID(extractedUnit)]
        if unitDamageTable then
            local totalDamage = 0
            for _, damageEvent in ipairs(unitDamageTable) do
                if GetTime() - damageEvent.Time < maximumTime then
                    totalDamage = totalDamage + damageEvent.Damage
                end
            end
            local maxHealth = UnitHealthMax(extractedUnit)
            local hpDamage = totalDamage / maxHealth * 100
            return hpDamage >= hp
        end
        return false
    end
end

Conditions.UnitHPDamagePhysicalAtLeastTakenSince = function(unit, hp, maximumTime)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local damageTable = scripty.Data.damageTable
        if not damageTable then
            return false
        end
        local unitDamageTable = scripty.Data.damageTable[UnitGUID(extractedUnit)]
        if unitDamageTable then
            local totalDamage = 0
            for _, damageEvent in ipairs(unitDamageTable) do
                if GetTime() - damageEvent.Time < maximumTime and damageEvent.DamageType == "physical" then
                    totalDamage = totalDamage + damageEvent.Damage
                end
            end
            local maxHealth = UnitHealthMax(extractedUnit)
            local hpDamage = totalDamage / maxHealth * 100
            return hpDamage >= hp
        end
        return false
    end
end

Conditions.UnitHPDamageMagicAtLeastTakenSince = function(unit, hp, maximumTime)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local damageTable = scripty.Data.damageTable
        if not damageTable then
            return false
        end
        local unitDamageTable = scripty.Data.damageTable[UnitGUID(extractedUnit)]
        if unitDamageTable then
            local totalDamage = 0
            for _, damageEvent in ipairs(unitDamageTable) do
                if GetTime() - damageEvent.Time < maximumTime and damageEvent.DamageType == "spell" then
                    totalDamage = totalDamage + damageEvent.Damage
                end
            end
            local maxHealth = UnitHealthMax(extractedUnit)
            local hpDamage = totalDamage / maxHealth * 100
            return hpDamage >= hp
        end
        return false
    end
end

Conditions.UnitSpeedLessThan = function(unit, amount)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        return GetUnitSpeed(extractedUnit) < amount
    end
end
--Pet
Conditions.NoPetOrDemonicSacrificeBuff = function()
    return function()
        if 
        UnitCastingInfo("player") == "Summon Succubus" or
        UnitCastingInfo("player") == "Summon Felhunter" or
        UnitCastingInfo("player") == "Summon Voidwalker" or
        UnitCastingInfo("player") == "Summon Felguard" or
        UnitCastingInfo("player") == "Summon Imp" then
            return false
        end

        if not UnitExists("pet") or UnitIsDeadOrGhost("pet") then
            return not Conditions.UnitHasAura("player", "Grimoire of Sacrifice", "HELPFUL")()
        end
    end
end

Conditions.HurtPet = function()
    return function()
        if UnitExists("pet") and not UnitIsDeadOrGhost("pet") and UnitHealth("pet") < UnitHealthMax("pet") * 0.65 then
            return true
        end
        return false
    end
end

Conditions.LivePet = function()
    return function()
        if UnitExists("pet") and not UnitIsDeadOrGhost("pet") then
            return true
        end
        return false
    end
end

Conditions.MissingPet = function()
    return function()
        if not UnitExists("pet") then
            return true
        end
        return false
    end
end


Conditions.NumberOfEnemiesGreaterThan = function(amount)
    return function()
        return scripty.Data.numberOfEnemies > amount
    end
end
Conditions.NumberOfMeleeEnemiesGreaterThan = function(amount)
    return function()
        return scripty.Data.numberOfMeleeEnemies > amount
    end
end
--Unit
Conditions.UnitIsUnit = function(unit, unit2)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local extractedUnit2 = scripty.Data.UnitFromUnit(unit2)
        if not extractedUnit then
            return false
        end
        if not extractedUnit2 then
            return false
        end
        return UnitIsUnit(extractedUnit, extractedUnit2)
    end
end
Conditions.UnitExists = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        return UnitExists(extractedUnit)
    end
end
Conditions.AbilityAvailable = function(ability)
    return function()
        return C_Spell.IsSpellUsable(ability)
    end
end
Conditions.UnitNameIs = function(unit, name)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        return UnitName(extractedUnit) == name
    end
end
Conditions.UnitCastingInterruptible = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit or not UnitExists(extractedUnit) then
            return false
        end
        local spell, text, texture, startTimeMS, endTimeMS, isTradeSkill, castID, notInterruptible, spellId = UnitCastingInfo(extractedUnit)
        if spell and not notInterruptible and not scripty.Conditions.SpellIsIgnored(unit, spellId)() then
            return true
        end
        channel, text, texture, startTimeMS, endTimeMS, isTradeSkill, notInterruptible, spellId = UnitChannelInfo(extractedUnit)
        if channel and not notInterruptible and not scripty.Conditions.SpellIsIgnored(unit, spellId)() then
            return true
        end
        return false
    end
end

Conditions.SpellIsIgnored = function(unit, spellId)
    return function()
        local enemyDataBlock = scripty.Data.EnemyDataBlock(unit)
        if enemyDataBlock and enemyDataBlock.spells[spellId] and enemyDataBlock.spells[spellId].ignore then
            return enemyDataBlock.spells[spellId].ignore
        end
        return false
    end
end

Conditions.StunnableUnitCasting = function (unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit or not UnitExists(extractedUnit) then
            return false
        end
        if not Conditions.UnitVulnerableToCC(unit)() then
            return false
        end
        local spell, text, texture, startTimeMS, endTimeMS, isTradeSkill, castID, notInterruptible, spellId = UnitCastingInfo(extractedUnit)
        if spell and not scripty.Conditions.SpellIsIgnored(unit, spellId)() then
            return true
        end
        channel, text, texture, startTimeMS, endTimeMS, isTradeSkill, notInterruptible, spellId = UnitChannelInfo(extractedUnit)
        if channel and not scripty.Conditions.SpellIsIgnored(unit, spellId)() then
            return true
        end
        return false
    end
end

Conditions.TargetIsReadyForInterrupt = function()
    return function()
        local testSpell = UnitCastingInfo("player")
        local testChannel = UnitChannelInfo("player")
        if testChannel or testSpell then
            return false
        end
        local spell, text, texture, startTimeMS, endTimeMS, isTradeSkill, castID, notInterruptible, spellId = UnitCastingInfo("target")
		if spell and not notInterruptible and not scripty.Conditions.SpellIsIgnored("target", spellId)() then
			local finish = endTimeMS / 1000 - GetTime()
			if finish < 2 then
                return true
			end
		end
		local channel, text, texture, startTimeMS, endTimeMS, isTradeSkill, notInterruptible, spellId = UnitChannelInfo("target")
		if channel and not notInterruptible and not scripty.Conditions.SpellIsIgnored("target", spellId)() then
			timeSinceStart = GetTime() - startTimeMS / 1000
            if timeSinceStart > 0.5 then
                return true
            end
		end
        return false
    end
end
Conditions.PlayerWasRecentlyMeleed = function()
    return function()
        scripty.Data.lastMeleedTime = scripty.Data.lastMeleedTime or GetTime() - 10
        return GetTime() - scripty.Data.lastMeleedTime < 2
    end
end
Conditions.TimeSinceAttackableGreaterThan = function(amount)
    return function()
        if Conditions.lastAttackableTime and Conditions.lastAttackableTime <= GetTime() - Conditions.lastAttackableTime then
            return false
        end
        return true
    end
end

Conditions.TargetWantsStun = function()
    return function()
        return scripty.Data.targetWantsStun
    end
end

Conditions.EnemyInMeleeNeedsStun = function()
    return function()
        return scripty.Data.EnemyInMeleeNeedsStun
    end
end

Conditions.NeedThunder = function()
    return function()
        return scripty.Data.needThunder
    end
end

Conditions.TargetHoldActivated = function()
    return function()
        if scripty.Data.targetHoldTime and scripty.Conditions.UnitAttackable("target")() then
            return GetTime() - scripty.Data.targetHoldTime < 15
        end
        return false
    end
end

Conditions.EnemyUnitInMelee = function(unit)
    return function()
        if UnitClass("player") == "Druid" and GetSpecialization() == 3 then 
            if Conditions.AbilityInRange("Mangle", unit)() then
                return true
            end
        end
        if UnitClass("player") == "Rogue" then
            if Conditions.AbilityInRange("Cheap Shot", unit)() then
                return true
            end
            if Conditions.AbilityInRange("Cheap Shot", unit)() then
                return true
            end
        end
        if UnitClass("player") == "Warrior" and GetSpecialization() == 3 then --Prot
            if Conditions.AbilityInRange("Shield Slam", unit)() then
                return true
            end
        end
        if UnitClass("player") == "Paladin" then 
            if Conditions.AbilityInRange("Rebuke", unit)() then
                return true
            end
        end
        if UnitClass("player") == "Monk" then 
            if Conditions.AbilityInRange("Rising Sun Kick", unit)() then
                return true
            end
        end
        if UnitClass("player") == "Death Knight" then 
            if Conditions.AbilityInRange("Death Strike", unit)() then
                return true
            end
        end
        if UnitClass("player") == "Shaman" then 
            if Conditions.AbilityInRange("Lava Lash", unit)() then
                return true
            end
        end
        return false
    end
end

Conditions.NumberOf10YardEnemiesGreaterThan = function(amount)
    return function()
        if scripty.Data.numberOf10YardEnemies and scripty.Data.numberOf10YardEnemies > amount then
            return true
        end
        return false
    end
end

Conditions.EnemyUnitWithin = function(unit, maxDistance)
    return function()
        local rc = LibStub("LibRangeCheck-3.0")
        local minRange, maxRange = rc:GetRange('target')
        if not maxRange then
            return false
        end
        return maxRange <= maxDistance
    end
end

Conditions.DangerousToCast = function()
    return function()
        if scripty.Data.dangerousToCast then
            return true
        end
        return false
    end
end

Conditions.NumberOf15YardEnemiesGreaterThan = function(amount)
    return function()
        if scripty.Data.numberOf15YardEnemies and scripty.Data.numberOf15YardEnemies > amount then
            return true
        end
        return false
    end
end

Conditions.FriendlyUnitWithin30Yards = function(unit)
    return function()
       return true
    end
end

Conditions.UnitAttackable = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        if scripty.Data.AttackDisabled then
            return false
        end
        if not UnitExists(extractedUnit) then
            return false
        end
        if scripty.Conditions.UnitHasAura(extractedUnit, "Noxious Fog", "HELPFUL")() then
            return false
        end
        if UnitHealth(extractedUnit) < 2 and UnitGetTotalAbsorbs(extractedUnit) == 0 then
            return false
        end
        if UnitIsDeadOrGhost(extractedUnit) then
            return false
        end
        if not scripty.Conditions.AbilityInRange("Smite", extractedUnit)() then
            return false
        end
        -- local rc = LibStub("LibRangeCheck-3.0")
        -- local minRange, maxRange = rc:GetRange(unit)
        -- if maxRange and maxRange > 70 then
        --     return false
        -- end
        if UnitName(unit) == "Explosives" then
            return true
        end
        if scripty.Data.forceAttackTargetGUID == UnitGUID(unit) then
            return true
        end
        local guid = UnitGUID(extractedUnit)
        scripty.Data.losTable = scripty.Data.losTable or {}
        local lastLosTime = scripty.Data.losTable[guid]
        if lastLosTime then
            if GetTime() - lastLosTime < 0.5 then
                return false
            end
        end
        if scripty.Data.currentRoutine.PriorityUnitFunction(extractedUnit)() == 0 then
            return false
        end
        if UnitIsFriend("player", unit) then
            return false
        end
        if scripty.Conditions.UnitHasImmunity(extractedUnit)() then
            return false
        end
        if scripty.Data.UnitDemandsForceAttack(extractedUnit) then
            return true
        end
        if UnitName(extractedUnit) == "Incorporeal Being" then
            return true
        end
        if scripty.Conditions.EnemyUnitInMelee(extractedUnit)() then
            return true
        end
        if not UnitIsThreatening(extractedUnit) then
            return false
        end
        
        return true
    end
end

Conditions.CanChangeTalents = function()
    return function()
        return C_ClassTalents.CanChangeTalents()
    end
end

Conditions.UnitHasImmunity = function(unit)
    return function()
        if scripty.Conditions.UnitHasAura(unit, "Divine Shield", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Iceblock", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Anti-Magic Shell", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Ice Block", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Aspect of the Turtle", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Spell Reflection", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Blessing of Spellwarding", "HELPFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Cyclone", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Fear", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Banish", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Polymorph", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Frost Trap", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Paralyze", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Time Stop", "HARMFUL")() then
            return true
        end
        if scripty.Conditions.UnitHasAura(unit, "Sap", "HARMFUL")() then
            return true
        end
        -- if scripty.Conditions.UnitHasAura(unit, "Blind", "HARMFUL")() then
        --     return true
        -- end
        return false
    end
end

Conditions.WeaponEnchanted = function()
    return function()
        local hasMainHandEnchant = GetWeaponEnchantInfo()
        return hasMainHandEnchant
    end
end

Conditions.OffHandEnchanted = function()
    return function()
        local _, _, _, _, hasOffHandEnchant = GetWeaponEnchantInfo()
        return hasOffHandEnchant
    end
end

Conditions.UnitIsPlayer = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if UnitIsPlayer(extractedUnit) then
            return true
        end
        return false
    end
end

Conditions.UnitHealable = function(unit)
    return function()
        if not unit or unit == "no one" then
            return false
        end
        if not UnitExists(unit) then
            return false
        end
        if UnitHealth(unit) < 2 then
            return false
        end
        local guid = UnitGUID(unit)
        scripty.Data.losTable = scripty.Data.losTable or {}
        local lastLosTime = scripty.Data.losTable[guid]
        if lastLosTime then
            if GetTime() - lastLosTime < 0.5 then
                return false
            end
        end
        if UnitIsDeadOrGhost(unit) then
            return false
        end
        if UnitIsCharmed(unit) then
			return false
		end
		if not UnitIsUnit(unit, "player") and not UnitIsUnit(unit, "pet") and not UnitInRange(unit) then
			return false
		end
        if scripty.Data.currentRoutine.PriorityUnitFunction(unit)() == 0 then
            return false
        end
        if not scripty.Conditions.UnitAuraCountLessThan(unit, "Tenderized", 8, "HARMFUL")() then
            return false
        end
        if not scripty.Conditions.UnitAuraCountLessThan(unit, "Necrotic Wound", 15, "HARMFUL")() then
            return false
        end
        return true
    end
end
Conditions.PlayerChannelRecentlyUsed = function()
    return function()
        if scripty.Data.lastPlayerChannelTime and scripty.Data.lastPlayerChannelTime - GetTime() < 1 then
            return true
        end
        return false
    end
end
Conditions.IsUnitInCombat = function(unit)
    return function()
        return UnitAffectingCombat(unit)
    end
end
Conditions.StaggerGreaterThan = function(amount)
    return function()
        local staggerDamage = UnitStagger("player") or 0
        local unitHealthMax = UnitHealthMax("player")
        local stagger = (staggerDamage / unitHealthMax) * 100
        return stagger > amount
    end
end

Conditions.UnitIsFriend = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if UnitIsFriend("player", extractedUnit) then
            return true
        end
        return false
    end
end

--Cooldowns
Conditions.SpellCountLessThan = function(spell, count)
    return function()
        local spellCount = C_Spell.GetSpellCastCount(spell)
        return count < spellCount
    end
end

Conditions.AbilityCDLessThan = function(ability, maximumRemaining)
    return function()
        local table = C_Spell.GetSpellCooldown(ability)
        if not table then
            return false
        end
        local enabled = table.isEnabled
        local start = table.startTime
        local duration = table.duration
        if not enabled then
            return false
        end
        local cdLeft = start + duration - GetTime()
        return cdLeft < maximumRemaining
    end
end
Conditions.InjuredNearbyGreaterThan = function(number)
    return function()
        if scripty.Data.injuredNearby and scripty.Data.injuredNearby > number then
            return true
        end
        return false
    end
end
Conditions.UnitIsNotPaladin = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if UnitClass(extractedUnit) == "Paladin" then
            return false
        end
        return true
    end
end
Conditions.AbilityOnCD = function (ability)
    return function()
        local start, duration, enabled = C_Spell.GetSpellCooldown(ability)
        if not enabled then
            return true
        end
        local cdLeft = start + duration - GetTime()
        return cdLeft > 2
    end
end
--Health
Conditions.PlayerFullHP = function() 
    return function()
         return UnitHealth("player") == UnitHealthMax("player") and UnitHealth("player") > 0
    end
end


--duplicate function, if you change this then change the one in dataharvesters.lua
function hitpoints(unit)
    local hp = UnitHealth(unit) / UnitHealthMax(unit) * 100
    local totalHealAbsorbs = UnitGetTotalHealAbsorbs(unit)
    if scripty.Conditions.UnitHasAura(unit, "Void Rift", "HELPFUL")() then
        totalHealAbsorbs = 1
    end
    if totalHealAbsorbs > 0 then
        hp = hp - 20
    end
    return hp
end

Conditions.UnitHPLessThan = function(unit, health)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit or extractedUnit == "no one" then
            return false
        end
        if not UnitExists(extractedUnit) then
            return false
        end
        local hp = hitpoints(extractedUnit)
        return hp < health
    end
end

Conditions.UnitUnderAttack = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        local unitGUID = UnitGUID(extractedUnit)
        scripty.Data.beatdownTable = scripty.Data.beatdownTable or {}
        local mostRecent = GetTime() - 999
        if scripty.Data.beatdownTable[unitGUID] and scripty.Data.beatdownTable[unitGUID].unitUnderAttackTime then 
            mostRecent = scripty.Data.beatdownTable[unitGUID].unitUnderAttackTime
        end
        return GetTime() - mostRecent < 3
    end
end

Conditions.EnemyIn35YardRange = function(unit)
    return function()
        return true
    end
end

Conditions.PlayerIsInPvPZone = function()
    return function()
        return C_PvP.IsPVPMap()
    end
end

--Loot
Conditions.PotentialLoot = function()
    return function()
        if UnitChannelInfo("player") then
            return false
        end
        if UnitCastingInfo("player") then
            return false
        end
        if UnitExists("boss1") or UnitExists("arena1") then
            return false
        end
        if C_ChallengeMode.IsChallengeModeActive() then
            return false
        end
        if C_PvP.IsBattleground() then
            return false
        end
        scripty.Data.lastLootTime = scripty.Data.lastLootTime or GetTime()
        scripty.Data.lastDeathTime = scripty.Data.lastDeathTime or GetTime()
        return scripty.Data.lastLootTime < scripty.Data.lastDeathTime
    end
end
--Auras
Conditions.UnitHasAura = function(unit, aura, filter)
    local filter = filter or "NONE"
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        if aura == "Tea of Serenity" then
            aura = 388519
        end
        local auraInfo = GetAuraInfo(extractedUnit, aura, filter)
        if auraInfo.name == "Atonement" then
            if UnitCastingInfo("player") == "Flash Heal" and scripty.Data.lastSpellTargetUnit and UnitIsUnit(scripty.Data.lastSpellTargetUnit, extractedUnit) then
                return true
            end
        end
        if auraInfo.name == "Atonement" then
            if UnitCastingInfo("player") == "Power Word: Radiance" and scripty.Data.lastSpellTargetUnit and UnitIsUnit(scripty.Data.lastSpellTargetUnit, extractedUnit) then
                return true
            end
        end
        -- if auraInfo.name == "Lightweaver" then
        --     if UnitCastingInfo("player") == "Heal" then
        --         return false
        --     end
        -- end
        if auraInfo.name == "Clearcasting" then
            if UnitCastingInfo("player") == "Regrowth" then
                return false
            end
        end
        if auraInfo.name == "Stellar Flare" then
            if UnitCastingInfo("player") == "Stellar Flare" then
                return true
            end
        end
        if auraInfo.name == "Lifecycles (Enveloping Mist)" then
            if UnitCastingInfo("player") == "Vivify" then
                return true
            end
        end
        if auraInfo.name == "Lifecycles (Vivify)" then
            if UnitCastingInfo("player") == "Enveloping Mist" then
                return true
            end
        end
        if auraInfo.name == "Eclipse (Lunar)" then
            if UnitCastingInfo("player") == "Wrath" and C_Spell.GetSpellCastCount("Wrath") < 2 then
                return true
            end
        end
        if auraInfo.name == "Eclipse (Solar)" then
            if UnitCastingInfo("player") == "Starfire" and C_Spell.GetSpellCastCount("Starfire") == 1 then
                return true
            end
        end
        return auraInfo.name ~= nil
    end
end
Conditions.UnitAuraDurationLessThan = function(unit, aura, duration, filter)
    local filter = filter or "NONE"
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not unit then
            return false
        end
        local auraInfo = GetAuraInfo(extractedUnit, aura, filter)
        if auraInfo.expirationTime then
            return auraInfo.expirationTime - GetTime() < duration 
        end
        return true
    end
end
Conditions.UnitVulnerableToCC = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        local unitName = UnitName(extractedUnit)
        if SophieScriptDB.CCImmunes[unitName] == 1 then
            return false
        end
        return true
    end
end
Conditions.UnitAuraCountLessThan = function(unit, aura, maximumCount, filter)
    local filter = filter or "NONE"
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not unit then
            return false
        end
        local auraInfo = GetAuraInfo(extractedUnit, aura, filter)
        if auraInfo.count then
            return auraInfo.count < maximumCount
        end
        return true
    end
end
Conditions.UnitHasAuraOrSpell = function(unit, aura, filter)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        local spell = UnitCastingInfo("player")
        if spell == aura and UnitIsUnit("target", extractedUnit) then
            return true
        end
        return Conditions.UnitHasAura(extractedUnit, aura, filter)()
    end
end
Conditions.UnitAuraOrSpellDurationLessThan = function(unit, aura, duration, filter)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        local spell = UnitCastingInfo("player")
        if spell == aura then
            return false
        end
        return Conditions.UnitAuraDurationLessThan(extractedUnit, aura, duration, filter)()
    end
end
Conditions.AbilityWasCastRecently = function(ability, seconds)
    return function()
        scripty.Data.AbilityCastTable = scripty.Data.AbilityCastTable or {}
        lastCastTime = scripty.Data.AbilityCastTable[ability]
        if lastCastTime and GetTime() - lastCastTime < seconds then
            return true
        end
        return false
    end
end

Conditions.PlayerInRaid = function()
    return function()
        return IsInRaid()
    end
end

Conditions.CDsEnabled = function()
    return function()
        return scripty.Data.CDsEnabled
    end
end
Conditions.AvoidingPenance = function()
    return function()
        local ability = scripty.Data.lastTrigger
        local timeSinceCast = scripty.Data.lastTriggerTime
        if timeSinceCast then
            timeSinceCast = GetTime() - timeSinceCast
        end
        if timeSinceCast and timeSinceCast > 1 then
            return true
        end
        if ability == "SPELL Penance" then
            return false
        end
        if ability == "MACRO Penance" then
            return false
        end
        if ability == "SPELL Ultimate Penitence" then
            return false
        end
        if UnitChannelInfo("player") == "Penance" then
            return false
        end
        if UnitChannelInfo("player") == "Ultimate Penitance" then
            return false
        end
        if UnitCastingInfo("player") == "Ultimate Penitance" then
            return false
        end
        return true
    end
end
Conditions.UnitRoleIs = function(unit, role)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        local role = UnitGroupRolesAssigned(extractedUnit)
        if role == role then
            return true
        end
        return false
    end
end

--Combination Checks
Conditions.AbilityUsable = function(ability)
    return function()
        --Class specific hacks
        --Affliction
        if ability == "Soul Tap" and scripty.Data.soulTapFail and GetTime() - scripty.Data.soulTapFail < 10 then
            return false
        end
        --end Affliction
        if UnitChannelInfo("player") ~= "Soothing Mist" and Conditions.PlayerIsChannelingAnything()() then
            return false  
        end
        if Conditions.AbilityCDLessThan(ability, 0.25)() == false then
            return false
        end
        if Conditions.AbilityAvailable(ability)() == false then
            return false
        end
        if Conditions.AbilityHasResources(ability)() == false then
            return false
        end
        if not scripty.Data.tauntEnabled and ability == "Dark Command" then
            return false
        end
        results = C_Spell.GetSpellInfo(ability)
        castTime = results.castTime
        --CastTime hacks\
        if (ability == "Heal" or ability == "Prayer of Healing") and Conditions.UnitHasAura("player", "Waste No Time", "HELPFUL")() then
            castTime = 0
        end
        if (ability == "Heal" or ability == "Prayer of Healing") and Conditions.UnitHasAura("player", "Divinity", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Nourish" and not Conditions.UnitAuraCountLessThan("player", "Wild Synthesis", 3, "PLAYER | HELPFUL")() then
            castTime = 0
        end
        if ability == "Regrowth" and Conditions.UnitHasAura("player", "Incarnation: Tree of Life", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Starfire" and Conditions.UnitHasAura("player", "Warrior of Elune", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Vampiric Touch" and Conditions.UnitHasAura("player", "Unfurling Darkness", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Vivify" and Conditions.UnitHasAura("player", "Vivacious Vivification", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Lava Burst" and Conditions.UnitHasAura("player", "Lava Surge", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Healing Surge" and Conditions.UnitHasAura("player", "Spiritwalker's Tidal Totem", "HELPFUL")() then
            castTime = 0
        end
        if (ability == "Healing Surge" or ability == "Healing Wave" or ability == "Chain Heal") and (Conditions.UnitHasAura("player", "Nature's Swiftness", "HELPFUL")() or Conditions.UnitHasAura("player", "Ancestral Swiftness", "HELPFUL")()) then
            castTime = 0
        end
        if ability == "Malefic Rapture" and Conditions.UnitHasAura("player", "Tormented Crescendo", "HELPFUL")() then
            castTime = 0
        end
        if 
            ability == "Drain Soul"         or 
            ability == "Drain Life"         or 
            ability == "Health Funnel"      or 
            ability == "Fleshcraft"         or 
            ability == "Mind Flay"          or 
            ability == "Mind Sear"          or
            ability == "Arcane Missiles"    or
            ability == "Disintegrate"       or
            ability == "Fire Breath"        or
            ability == "Eternity Surge"     or
            ability == "Void Torrent"       or
            ability == "Soothing Mist"
        then
            castTime = 1
        end
        if ability == "Arcane Missiles" and Conditions.UnitHasAura("player", "Clearcasting", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Scorch" then
            castTime = 0
        end
        if ability == "Steady Shot" then
            castTime = 0
        end
        if ability == "Rapid Fire" then
            castTime = 0
        end
        if ability == "Shifting Power" then
            castTime = 1
        end
        if Conditions.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL")() and not Conditions.UnitAuraCountLessThan("player", "Maelstrom Weapon", 5, "HELPFUL")() then
            castTime = 0
        end
        if ability == "Pyroblast" and Conditions.UnitHasAura("player", "Hot Streak!", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Flash of Light" and GetSpecialization() == 3 then
            castTime = 0
        end
        if (ability == "Lightning Bolt" or ability == "Chain Lightning") and Conditions.UnitHasAura("player", "Stormkeeper", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Holy Light" and Conditions.UnitHasAura("player", "Hand of Divinity", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Enveloping Mist" and Conditions.UnitHasAura("player", "Thunder Focus Tea", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Enveloping Mist" and Conditions.AbilityUsable("player", "Thunder Focus Tea", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Enveloping Mist" and Conditions.UnitHasAura("player", "Tea of Serenity", "HELPFUL")() then
            castTime = 0
        end
        if not Conditions.UnitAuraDurationLessThan("player", "Foresight", 2, "HELPFUL")() then
            castTime = 0
        end
        if not Conditions.UnitAuraDurationLessThan("player", "Ice Floes", 2, "HELPFUL")() then
            castTime = 0
        end
        --End CastTime hacks
        local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
        if castTime > 0 and auraInfo.name then
            return false
        end
        if castTime > 0 and Conditions.PlayerMoving()() then
            if Conditions.UnitHasAura("player", "Spiritwalker's Grace", "HELPFUL")() then
               return true 
            elseif Conditions.UnitHasAura("player", "Hover", "HELPFUL")() and (ability ~= "Fire Breath" and ability ~= "Eternity Surge") then
                return true
            else
                return false
            end
        end
        return true
    end
end
Conditions.AbilityUsableIgnoreCost = function(ability)
    return function()
        --Class specific hacks
        --Affliction
        if ability == "Soul Tap" and scripty.Data.soulTapFail and GetTime() - scripty.Data.soulTapFail < 10 then
            return false
        end
        --end Affliction
        if UnitChannelInfo("player") ~= "Soothing Mist" and Conditions.PlayerIsChannelingAnything()() then
            return false
        end
        if Conditions.AbilityCDLessThan(ability, 0.25)() == false then
            return false
        end
        if Conditions.AbilityAvailable(ability)() == false then
            return false
        end
        if not scripty.Data.tauntEnabled and ability == "Dark Command" then
            return false
        end
        if not scripty.Data.tauntEnabled and ability == "Death Grip" then
            return false
        end
        name, rank, icon, castTime = C_Spell.GetSpellInfo(ability)
        --CastTime hacks
        if ability == "Mind Blast" and not Conditions.UnitAuraCountLessThan("player", "Mind Melt", 1, "HELPFUL")() then
            castTime = 0
        end
        if ability == "Vampiric Touch" and Conditions.UnitHasAura("player", "Unfurling Darkness", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Vivify" and Conditions.UnitHasAura("player", "Vivacious Vivification", "HELPFUL")() then
            castTime = 0
        end
        if ability == "Malefic Rapture" and Conditions.UnitHasAura("player", "Tormented Crescendo", "HELPFUL")() then
            castTime = 0
        end
        --hack for channel spells T_T
        if ability == "Drain Soul" or ability == "Drain Life" or ability == "Health Funnel" or ability == "Fleshcraft" then
            castTime = 1
        end
        if ability == "Scorch" then
            castTime = 0
        end
        if ability == "Pyroblast" and Conditions.UnitHasAura("player", "Hot Streak!", "HELPFUL") then
            castTime = 0
        end
        if castTime > 0 and Conditions.PlayerMoving()() then
            return false
        end
        --End CastTime hacks
        local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
        if castTime > 0 and auraInfo.name then
            return false
        end
        return true
    end
end 
Conditions.AbilityUsableIgnoreMovement = function(ability)
    return function()
        if Conditions.AbilityCDLessThan(ability, 0.25)() == false then
            return false
        end
        if Conditions.AbilityAvailable(ability)() == false then
            return false
        end
        if Conditions.AbilityHasResources(ability)() == false then
            return false
        end
        return true
    end
end

Conditions.VanishWillEndCombat = function()
    return function()
        if UnitExists("target") then
            if Conditions.UnitHasThreatOnPartyMember("target")() then
                return false
            end
        end
        return true
    end
end

Conditions.UnitHasThreatOnPartyMember = function(unit)
    return function()
        local groupSize = GetNumGroupMembers()
        if (groupSize < 1) then
            return false
        end
        if (groupSize > 40) then
            groupSize = 40
        end
        local groupString = nil
        if (IsInRaid()) then
            groupString = "raid"
        else
            groupString = "party"	
        end
        for i = 1, groupSize do
            local currentUnit = groupString .. i
            if UnitExists(unit) and UnitExists(currentUnit) then
                local status = UnitThreatSituation(currentUnit, unit)
                if status ~= nil then
                    return true
                end
                local isTanking, status, threatpct, rawthreatpct, threatvalue = UnitDetailedThreatSituation(currentUnit, unit)
                if threatvalue and threatvalue > 0 then
                    return true
                end
            end
            if UnitIsPlayer(unit) then
                return true
            end
        end
    end
end

scripty.Conditions = Conditions
--helper functions
function UnitIsThreatening(unit)
	local groupSize = GetNumGroupMembers()
	if (groupSize > 40) then
		groupSize = 40
	end
	local groupString = nil
	if (IsInRaid()) then
		groupString = "raid"
	else
		groupString = "party"	
	end
	for i = 0, groupSize + 1 do
		local currentUnit = groupString .. i
		if (i == 0) then
			currentUnit = "player"
		end
		if (i == groupSize + 1) then
			currentUnit = "pet"
		end
		if UnitExists(unit) and UnitExists(currentUnit) then
			local status = UnitThreatSituation(currentUnit, unit)
			if status ~= nil then
				return true
			end
			local isTanking, status, threatpct, rawthreatpct, threatvalue = UnitDetailedThreatSituation(currentUnit, unit)
			if threatvalue and threatvalue > 0 then
				return true
			end
			if UnitIsUnit(unit .. "target", currentUnit) then
				return true
			end	
		end
        if UnitIsPlayer(unit) then
            return true
        end
        if scripty.Data.forceAttackMode then
            return true 
        end
        if scripty.Data.forceAttackTargetGUID == UnitGUID(unit) then
            return true
        end
        if scripty.Data.currentRoutine.ForceAttackFunction(unit)() then
            return true
        end
        scripty.Data.attackedTable = scripty.Data.attackedTable or {}
        if scripty.Data.attackedTable[UnitGUID(unit)] and GetTime() - scripty.Data.attackedTable[UnitGUID(unit)] < 5 then
            return true
        end 
	end
	return false
end
--Demonology Specific
Conditions.ImpCountGreaterThan = function(count)
    return function()
        local numberFound = 0
        local impTable = scripty.Data.ImpTable or {}
        for _, value in pairs(impTable) do
            if value and GetTime() - value < 10 then
                numberFound = numberFound + 1
            end
        end
        return numberFound > count
    end
end
Conditions.MultiTargetEnemiesGreaterThan = function(count)
    return function()
        if scripty.Data.lastMultihit and GetTime() - scripty.Data.lastMultihit < 2 and scripty.Data.NumberOfMultiHits then
            return scripty.Data.NumberOfMultiHits > count
        end
    end
end
--Affliction Specific
Conditions.NumberOfUnitsMissingDebuffGreaterThan = function(debuff, amount)
    return function()
        local numberFound = 0
        for i = 1, 40 do
            local unitToScan = "nameplate" .. i
            if UnitExists(unitToScan) and scripty.Conditions.UnitAttackable(unitToScan)() then
                if not scripty.Conditions.UnitHasAura(unitToScan, debuff, "PLAYER")() then
                    numberFound = numberFound + 1
                end
            end
        end
        return numberFound > amount
    end
end
Conditions.DotRecentlyTicked = function()
    return function()
        if scripty.Data.lastDotTick then
            return GetTime() - scripty.Data.lastDotTick < 2
        else
            return false
        end
    end
end
Conditions.ActiveDotCountGreaterThan = function(count)
    return function()
        return scripty.Data.numberOfActiveDots > count
    end
end
Conditions.UnstableRecentlyTicked = function()
    return function()
        if scripty.Data.lastUnstableTick then
            return GetTime() - scripty.Data.lastUnstableTick < 2
        else
            return false
        end
    end
end
Conditions.UnitLowHealthShit = function(unit)
    return function()
        return UnitHealth(unit) < UnitHealth("player") * 4
    end
end
--Death Knight Specific
Conditions.DeathKnightRunesGreaterThan = function(amount) 
    return function()
        local runeAmount = 0
        for i = 1,6 do
          local start, duration, runeReady = GetRuneCooldown(i)
          if runeReady == true then
            runeAmount = runeAmount + 1
          end
        end
        return runeAmount > amount
    end
end
Conditions.DeathKnightIsTargetInMelee = function()
    return function()
        if not scripty.Conditions.UnitAttackable("target")() then
            return false
        end
        if IsSpellInRange("Death Strike", "target") ~= 1 then
            return false
        end
        return true
    end
end
Conditions.DeathKnightUnitNeedsTaunt = function(unit)
    return function()
        if not unit then
            return false
        end
        if not UnitExists(unit) then
            return false
        end
        if not scripty.Conditions.UnitAttackable(unit)() then
            return false
        end
        if UnitExists(unit .. "target") and UnitGroupRolesAssigned(unit .. "target") == "TANK" then
            return false
        end
        status = UnitThreatSituation("player", unit)
        if status ~= nil and status < 2 then
            return true
        end
        return false
    end
end
--Paladin
Conditions.ShouldInterruptCurrentSpellPaladin = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and spell == "Repentance" and UnitName("target") == "Incorporeal Being" and not UnitCastingInfo("target") then
            return true
        end
        if spell and spell == "Flash of Light" and scripty.Data.lastSpellTargetUnit and not scripty.Conditions.UnitHPLessThan(scripty.Data.lastSpellTargetUnit, 92)() then
            return true
        end
        if spell and spell == "Flash of Light" and not scripty.Conditions.UnitHasAura("player", "Infusion of Light", "HELPFUL")() then
            return true
        end
        return false
    end
end
--mistweaver
Conditions.MistHealingActive = function()
    return function()
        channel = UnitChannelInfo("player")
        if channel ~= "Soothing Mist" then
            return false
        end
        if not scripty.Conditions.UnitHPLessThan({name="bestHealTarget"}, 95)() then
            return false
        end
        return true
    end
end

--mistweaver
Conditions.ShouldInterruptCurrentSpellMist = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and spell == "Enveloping Mist" then
            return true
        end
        if spell and spell == "Sheilun's Gift" and scripty.Data.lastSpellTargetUnit and not scripty.Conditions.UnitHPLessThan(scripty.Data.lastSpellTargetUnit, 99)() then
            return true
        end
        if channel and channel == "Mana Tea" and scripty.Conditions.UnitHPLessThan({name="bestHealTarget"}, 50)() and not scripty.Conditions.PlayerResourceMinimum(SOPHIE_POWER_MANA, 21)() then
            return true
        end
        if channel and channel == "Mana Tea" and scripty.Conditions.PlayerResourceMinimum(SOPHIE_POWER_MANA, 99)() then
            return true
        end
        return false
    end
end

--Holy Specific
Conditions.ShouldInterruptCurrentSpellHoly = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and (spell == "Heal" or spell == "Flash Heal" or spell == "Prayer of Healing") and scripty.Data.lastSpellTargetUnit and hitpoints(scripty.Data.lastSpellTargetUnit) > 95 then
            return true
        end
        if spell and spell == "Heal" and not scripty.Conditions.UnitHasAura("player", "Lightweaver", "HELPFUL")()  then
            return true
        end
        if spell and (spell == "Holy Fire" or spell == "Smite" or spell == "Holy Word: Chastise") and scripty.Conditions.UnitHPLessThan({name="bestHealTarget"}, 50)() then
            return true
        end
        -- if spell and (spell == "Flash Heal") and not Conditions.UnitAuraCountLessThan("player", "Lightweaver", 2, "PLAYER | HELPFUL")() then
        --     return true
        -- end
        return false
    end
end
Conditions.SanctifyTargetsGreaterThan = function(amount)
    return function()
        return scripty.Data.sanctifyTargets > amount
    end
end
--Devastation
Conditions.BronzeNeeded = function()
    return function()
        return scripty.Data.groupNeedsBronze
    end
end
--Disc Specific
Conditions.FortitudeNeeded = function()
    return function()
        return scripty.Data.groupNeedsFort
    end
end
Conditions.RadianceTargetsGreaterThan = function(amount)
    return function()
        return scripty.Data.radianceTargets > amount
    end
end
Conditions.ShouldInterruptCurrentSpellDisc = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        return false
    end
end

Conditions.ShouldInterruptCurrentSpellRestoShammy = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and (spell == "Healing Surge" or spell == "Healing Wave" or spell == "Chain Heal") and scripty.Data.lastSpellTargetUnit and hitpoints(scripty.Data.lastSpellTargetUnit) > 95 then
            return true
        end
        if spell and (spell == "Chain Lightning" or spell == "Lava Burst" or spell == "Lightning Bolt") and scripty.Data.bestHealTarget and hitpoints(scripty.Data.bestHealTarget) < 60 then
            return true
        end
        return false
    end
end

Conditions.ShouldInterruptCurrentSpellDemo = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and spell == "Demonbolt" then
            return true
        end
        if channel and channel == "Drain Life" and hitpoints("player") > 99 then
            return true
        end
        if channel and channel == "Health Funnel" and hitpoints("pet") > 99 then
            return true
        end
        return false
    end
end

Conditions.ShouldInterruptCurrentSpellTree = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and (spell == "Regrowth" or spell == "Nourish") and scripty.Data.lastSpellTargetUnit and hitpoints(scripty.Data.lastSpellTargetUnit) > 99 then
            return true
        end
        return false
    end
end

Conditions.ShouldInterruptCurrentSpellBoomie = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and spell == "Regrowth" and scripty.Data.lastSpellTargetUnit and hitpoints(scripty.Data.lastSpellTargetUnit) > 90 then
            return true
        end
        if spell and spell == "Wrath" and scripty.Conditions.UnitHasAura("player", "Eclipse (Lunar)", "PLAYER | HELPFUL")() and scripty.Conditions.UnitHasAura("player", "Moonkin Form", "PLAYER | HELPFUL")() then
            return true
        end
        if spell and spell == "Stellar Flare" and scripty.Conditions.UnitHasAura("target", "Stellar Flare", "PLAYER | HARMFUL")() then
            return true
        end
        return false
    end
end

Conditions.DeathKnightBloodBoilIsNeeded = function()
    return function()
        return scripty.Data.bloodBoilNeeded
    end
end

--WW Specific
Conditions.KeepCombo = function(ability)
    return function()
        return ability ~= scripty.Data.MonkComboMove
    end
end
Conditions.PlayerUnderMagicAssault = function()
    return function()
        if scripty.Data.lastMagicBeatDownTime and scripty.Data.lastMagicBeatDownTime < 5 then
            return true
        end
        return false
    end
end

Conditions.PlayerHasMagicDebuff = function()
    return function()
        if scripty.Data.magicTarget and UnitIsUnit(scripty.Data.magicTarget, "player") then
            return true
        end
        return false
    end
end

Conditions.LODTargetsGreaterThan = function(numberOfTargets)
    return function()
        if scripty.Data.lodTargets and scripty.Data.lodTargets > numberOfTargets then
            return true
        end
        return false
    end
end

--ProtPala Specific
Conditions.ProtPalaUnitNeedsTaunt = function(unit)
    return function()
        local extractedUnit = scripty.Data.UnitFromUnit(unit)
        if not extractedUnit then
            return false
        end
        if not UnitExists(extractedUnit) then
            return false
        end
        if UnitIsPlayer(extractedUnit) then
            return false
        end
        if not scripty.Conditions.UnitAttackable(extractedUnit)() then
            return false
        end
        if UnitExists(extractedUnit .. "target") and UnitGroupRolesAssigned(extractedUnit .. "target") == "TANK" then
            return false
        end
        status = UnitThreatSituation("player", extractedUnit)
        if status ~= nil and status < 2 then
            return true
        end
        return false
    end
end

Conditions.MissingDiscPanic = function()
    return function()
        if Conditions.AbilityWasCastRecently("Luminous Barrier", 15)() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Rapture", 15)() then
            return false
        end
        return true
    end
end

Conditions.MissingHolyPallyPanic = function()
    return function()
        if Conditions.UnitHasAura("player", "Avenging Wrath", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Avenging Wrath", 15)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Aura Mastery", "HELPFUL")() then
            return false
        end
        if Conditions.UnitHasAura("player", "Tyr's Deliverance", "HELPFUL")() then
            return false
        end
        return true
    end
end

Conditions.MissingPallyDefensive = function()
    return function()
        if not Conditions.UnitHPLessThan("player", 65)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Divine Shield", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Divine Shield", 8)() then
            return false
        end
        -- if not Conditions.AbilityOnCD("Divine Shield")() then
        --     return false
        -- end
        if Conditions.UnitHasAura("player", "Ardent Defender", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Ardent Defender", 8)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Guardian of Ancient Kings", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Guardian of Ancient Kings", 8)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Avenging Wrath", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Avenging Wrath", 15)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Sentinel", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Sentinel", 15)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Seraphim", "HELPFUL")() then
            return false
        end
        if Conditions.AbilityWasCastRecently("Seraphim", 8)() then
            return false
        end
        if Conditions.UnitHasAura("player", "Pain Suppression", "HELPFUL")() then
            return false
        end
        if Conditions.UnitHasAura("player", "Power Word: Barrier", "HELPFUL")() then
            return false
        end
        if Conditions.UnitHasAura("player", "Ironbark", "HELPFUL")() then
            return false
        end
        if Conditions.UnitHasAura("player", "Guardian Spirit", "HELPFUL")() then
            return false
        end
        if Conditions.UnitHasAura("player", "Life Cocoon", "HELPFUL")() then
            return false
        end
        return true
    end
end


--Prot Specific
Conditions.ShoutNeeded = function()
    return function()
        return scripty.Data.groupNeedsShout
    end
end

Conditions.SpellReflectNOW = function()
    return function()
        return scripty.Data.SpellReflectNOW
    end
end

Conditions.UserDemandsCharge = function()
    return function()
        if scripty.Data.lastChargeTime then
            if scripty.AbilityCastTable then
                if scripty.Data.AbilityCastTable.Charge and scripty.Data.AbilityCastTable.Charge >= scripty.Data.lastChargeTime then
                    return false
                end
                -- if scripty.Data.AbilityCastTable["Shield Charge"] and scripty.Data.AbilityCastTable["Shield Charge"] >= scripty.Data.lastChargeTime then
                --     return false
                -- end
                return GetTime() - scripty.Data.lastChargeTime < 5
            end
            return GetTime() - scripty.Data.lastChargeTime < 5
        end
        return false
    end
end


--Assass Specific
Conditions.StealthAbilitiesUsable = function()
    return function()
        if Conditions.UnitHasAura("player", "Stealth", "HELPFUL")() then
            return true
        end
        if Conditions.UnitHasAura("player", "Vanish", "HELPFUL")() then
            return true
        end
        if Conditions.UnitHasAura("player", "Shadow Dance", "HELPFUL")() then
            return true
        end
        if Conditions.UnitHasAura("player", "Subterfuge", "HELPFUL")() then
            return true
        end
        return false
    end
end

--Laser Bear Specific
Conditions.MarkNeeded = function()
    return function()
        return scripty.Data.groupNeedsMark
    end
end

Conditions.ShapeshiftFormIs = function(form)
    return function()
        return GetShapeshiftForm("player") == form
    end
end

Conditions.EnemyTargetNeedsSooth = function()
    return function()
        AuraUtil.ForEachAura("target", "HELPFUL", 40, function(name, icon, count, dispelType, duration, expirationTime, source, isStealable, nameplateShowPersonal, spellId, canApplyAura, isBossDebuff, castByPlayer, nameplateShowAll, timeMod)
            if name and dispelType == "" then
                return true
            end
        end)
        return false
    end
end

--Arcane Specific
Conditions.ArcaneNeeded = function()
    return function()
        return scripty.Data.groupNeedsArcane
    end
end

Conditions.UnitHasStealable = function(unit)
    return function()
        for i=1,40 do
            local name,_,_,debufftype, _, expirationTime,_,cansteal = UnitAura(unit, i);
            if cansteal and expirationTime and expirationTime - GetTime() > 5 then
                return true
            end
        end
        return false
    end
end

Conditions.UnitNeedsPurge = function(unit)
    return function()
        for i=1,40 do
            local name,_,_,debufftype, _, expirationTime,_,cansteal = UnitAura(unit, i);
            if cansteal then
                return true
            end
        end
        return false
    end
end

--Fire Specific
Conditions.ShouldInterruptCurrentSpellFire = function()
    return function()
        channel = UnitChannelInfo("player")
        spell, displayName, icon, startTime, endTime = UnitCastingInfo("player")
        if spell or channel then
            local auraInfo = GetAuraInfo("player", "Quake", "HARMFUL")
            name, icon, count, debuffType, duration, expirationTime, unitCaster = auraInfo.name, auraInfo.icon, auraInfo.count, auraInfo.debuffType, auraInfo.duration, auraInfo.expirationTime, auraInfo.unitCaster
            if name and (channel or expirationTime + 0.25 < endTime) then
                return true
            end
        end	
        if spell and spell == "Pyroblast" and Conditions.UnitHasAura("player", "Hot Streak!", "HELPFUL")() then
            return true
        end
       
        return false
    end
end

--Outlaw Specific
Conditions.ShouldRerollBuffs = function()
    return function()
        return (RogueBuffCount() < 1 or Conditions.UnitHasAura("player", "Loaded Dice", "HELPFUL")())
    end
end

Conditions.ShouldKeepBuffs = function()
    return function()
        return RogueBuffCount() > 2
    end
end

function RogueBuffCount()
    local buffCount = 0
    if Conditions.UnitHasAura("player", "Broadside", "HELPFUL")() then
        buffCount = buffCount + 1
    end
    if Conditions.UnitHasAura("player", "Buried Treasure", "HELPFUL")() then
        buffCount = buffCount + 1
    end
    if Conditions.UnitHasAura("player", "Grand Melee", "HELPFUL")() then
        buffCount = buffCount + 1
    end
    if Conditions.UnitHasAura("player", "Ruthless Precision", "HELPFUL")() then
        buffCount = buffCount + 1
    end
    if Conditions.UnitHasAura("player", "Skull and Crossbones", "HELPFUL")() then
        buffCount = buffCount + 1
    end
    if Conditions.UnitHasAura("player", "True Bearing", "HELPFUL")() then
        buffCount = buffCount + 1
    end
    return buffCount
end

--RestoShammy
Conditions.EarthLivingNeeded = function()
    return function()
        return not GetWeaponEnchantInfo()
    end
end

Conditions.SpiritLinkTime = function()
    return function()
        return scripty.Data.spiritLinkTime
    end
end

--Tree
Conditions.LifebloomActive = function()
    return function()
        if scripty.Data.numberOfPotentialBlooms > 1 then
            return scripty.Data.numberOfActiveBlooms > 1 
        else
            return scripty.Data.numberOfActiveBlooms == 1 
        end
    end
end

Conditions.EffloActive = function()
    return function()
        if scripty.Data.lastEffloTick then
            return GetTime() - scripty.Data.lastEffloTick < 2 
        end
        return false
    end
end

Conditions.BloomMovable = function()
    return function()
        return scripty.Data.bloomMovable
    end
end