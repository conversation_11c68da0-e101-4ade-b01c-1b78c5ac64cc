local addonName, scripty = ...

SOPHIE_POWER_MANA = 0
SOPHIE_POWER_RAGE = 1
SOPHIE_POWER_FOCUS = 2
SOPHIE_POWER_ENERGY = 3
SOPHIE_POWER_COMBOPOINTS = 4
SOPHIE_POWER_RUNES = 5
SOPHIE_POWER_RUNICPOWER = 6
SOPHIE_POWER_SOULSHARDS = 7
SOPHIE_POWER_LUNARPOWER = 8
SOPHIE_POWER_HOLYPOWER = 9
SOPHIE_POWER_ALTERNATE = 10
SOPHIE_POWER_MAELSTROM = 11
SOPHIE_POWER_CHI = 12
SOPHIE_POWER_INSANITY = 13
SOPHIE_POWER_OBSELETE1 = 14
SOPHIE_POWER_OBSELETE2 = 15
SOPHIE_POWER_ARCANECHARGES = 16
SOPHIE_POWER_FURY = 17
SOPHIE_POWER_PAIN = 18

SOPHIE_ITEM_HEALINGPOTION = 244838
SOPHIE_ITEM_MANAPOTION = 212241
SOPHIE_ITEM_HEALTHSTONE = 5512
SOPHIE_ITEM_LOOTARANG = 60854
SOPHIE_ITEM_PREPOTION_INTELLECT = 191383
SOPHIE_ITEM_FLASK = 191318
SOPHIE_ITEM_OIL = 224107
SOPHIE_ITEM_OIL2 = 198162
SOPHIE_ITEM_INNERPOTION = 191367 --silver quality, gold is 191368
SOPHIE_ITEM_NOGGENFOGGER = 8529
SOPHIE_ITEM_RUNE = 211495

SOPHIE_SHAPESHIFTFORM_BEAR = 1
SOPHIE_SHAPESHIFTFORM_CAT = 2
SOPHIE_SHAPESHIFTFORM_TRAVEL = 3
SOPHIE_SHAPESHIFTFORM_MOONKIN = 4
SOPHIE_SHAPESHIFTFORM_MOUNT = 5
SOPHIE_SHAPESHIFTFORM_FLIGHT = 6

local Triggers = {}
Triggers.WW = {}
Triggers.Mist = {}
Triggers.Fire = {}
Triggers.Disc = {}
Triggers.Holy = {}
Triggers.BloodDK = {}
Triggers.Affliction = {}
Triggers.Demo = {}
Triggers.Destro = {}
Triggers.Marks = {}
Triggers.Shadow = {}
Triggers.ProtPala = {}
Triggers.Prot     = {}
Triggers.SophieDK = {}
Triggers.LaserBear = {}
Triggers.Arcane = {}
Triggers.Subtlety = {}
Triggers.Outlaw = {}
Triggers.Assass = {}
Triggers.Devastation = {}
Triggers.Brew = {}
Triggers.RestoShammy = {}
Triggers.Tree = {}
Triggers.BM = {}
Triggers.Boomkin = {}
Triggers.HolyPala = {}
Triggers.Ret = {}
Triggers.Enhance = {}
Triggers.Elemental = {}

local sc = scripty.Conditions

local function Inverse(toBeReversed) 
    return function() return not toBeReversed() end
end

--General Stuff
Triggers.Friendlies = {
    Name="Dummy Trigger",
    Binding="friend",
    Conditions= {
        function() return false end,
    }
}
Triggers.HunterTarget = {
    Name="HunterTarget",
    Binding=scripty.Bindings.HunterTarget, 
    Conditions={
        Inverse(sc.TargetIsHighestPriority()),
        Inverse(sc.TargetHoldActivated()),
    }
}
Triggers.AcquireTarget = {
    Name="AcquireTarget",
    Binding=scripty.Bindings.AcquireTarget, 
    Conditions={
        Inverse(sc.TargetIsHighestPriority()),
        Inverse(sc.TargetHoldActivated()),
    }
}
Triggers.LootStuff = {
    Name="Loot stuff",
    Binding=scripty.Bindings.Loot,
    Conditions={
        sc.ToyOffCD(SOPHIE_ITEM_LOOTARANG),
        sc.PotentialLoot(),
        -- sc.TimeSinceAttackableGreaterThan(0.25),
        Inverse(sc.PlayerMoving()),
        Inverse(sc.MistHealingActive()),
        Inverse(sc.PlayerIsFlying()),
    }
}
Triggers.UseTrinket = {
    Name="Trinket",
    Binding=scripty.Bindings.Trinket,
    Conditions={
        sc.TrinketIsReady(1),
        sc.UnitAttackable("target"),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
    }
}
Triggers.UseTrinket2 = {
    Name="Trinket2",
    Binding=scripty.Bindings.Trinket2,
    Conditions={
        sc.TrinketIsReady(2),
        sc.UnitAttackable("target"),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
    }
}

Triggers.UsePriestTrinket2 = {
    Name="Priesty Trinket2",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.FocusTrinket2,
    Conditions={
        sc.TrinketIsReady(2),
        sc.UnitHPLessThan({name="bestHealTarget"}, 35),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
    }
}

Triggers.WillToSurvive = {
    Name="Will to Survive",
    Binding=scripty.Bindings.WillToSurvive,
    Conditions={
        sc.AbilityUsable("Will to Survive"),
        sc.StunnedRecently(),
    }
}

Triggers.Prepot = {
    Name="Flask",
    Binding=scripty.Bindings.Prepot,
    Conditions={
        sc.UnitAttackable("target"),
        sc.ItemIsReady(SOPHIE_ITEM_PREPOTION_INTELLECT),
    }
}

Triggers.Flask = {
    Name="Flask",
    Binding=scripty.Bindings.Flask,
    Conditions={
        Inverse(sc.UnitHasAura("player", "Phial of the Eye in the Storm", "HELPFUL")),
        sc.ItemIsReady(SOPHIE_ITEM_FLASK),
    }
}

Triggers.Oil = {
    Name="Oil",
    Binding=scripty.Bindings.Oil,
    Conditions={
        Inverse(sc.WeaponEnchanted()),
        Inverse(sc.PlayerMoving()),
        sc.ItemIsReady(SOPHIE_ITEM_OIL),
    }
}

Triggers.AugmentRune = {
    Name="Augment Rune",
    Binding=scripty.Bindings.AugmentRune,
    Conditions={
        sc.ItemIsReady(SOPHIE_ITEM_RUNE),
        Inverse(sc.UnitHasAura("player", "Draconic Augmentation", "HELPFUL")),
    }
}

Triggers.Oil2 = {
    Name="Oil",
    Binding=scripty.Bindings.Oil2,
    Conditions={
        Inverse(sc.WeaponEnchanted()),
        Inverse(sc.PlayerMoving()),
        sc.ItemIsReady(SOPHIE_ITEM_OIL2),
    }
}

Triggers.NoggenFogger = {
    Name="Shrinkies",
    Binding=scripty.Bindings.NoggenFogger,
    Conditions={
        sc.ItemIsReady(SOPHIE_ITEM_NOGGENFOGGER),
        Inverse(sc.UnitHasAura("player", "Noggenfogger Elixir", "HELPFUL")),
    }


}

Triggers.Mist.SpinningCraneKickAoe = {
    Name="Spinning Crane Kick",
    Binding=scripty.Bindings.SpinningCraneKick,
    Conditions={
        sc.AbilityUsable("Spinning Crane Kick"),
        sc.NumberOfMeleeEnemiesGreaterThan(1),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
    }
}

Triggers.Mist.SpinningCraneKickProc = {
    Name="Spinning Crane Kick Proc",
    Binding=scripty.Bindings.SpinningCraneKick,
    Conditions={
        sc.AbilityUsable("Spinning Crane Kick"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Dance of Chi-Ji", "HELPFUL"),
        sc.NumberOfMeleeEnemiesGreaterThan(0)
    }
}

Triggers.Mist.CancelNoggenFogger = {
    Name="ShrinkiesCancel",
    Binding=scripty.Bindings.CancelNoggenFogger,
    Conditions={
        sc.UnitHasAura("player", 16591, "HELPFUL"),
    }
}

Triggers.HealthPotion = {
    Name="Healthpot",
    Binding=scripty.Bindings.HealthPotion,
    Conditions={
        sc.UnitHPLessThan("player", 35),
        sc.ItemIsReady(SOPHIE_ITEM_HEALINGPOTION),
        sc.AvoidingPenance(),
    }
}

Triggers.ManaPotion = {
    Name="Manapot",
    Binding=scripty.Bindings.ManaPotion,
    Conditions={
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 70)),
        sc.ItemIsReady(SOPHIE_ITEM_MANAPOTION),
        sc.AvoidingPenance(),
    }
}

Triggers.InnervatePotion = {
    Name="InnerPotion",
    Binding=scripty.Bindings.InnervatePotion,
    Conditions={
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 70)),
        sc.ItemIsReady(SOPHIE_ITEM_INNERPOTION),
    }
}

--Elemental
Triggers.Elemental.HealingSurge = {
    Name="Healing Surge",
    Focus="player",
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.UnitHPLessThan("player", 65),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 40),
    }
}

Triggers.Elemental.LightningShield = {
    Name="Lightning Shield",
    Binding=scripty.Bindings.LightningShield,
    Conditions={
        sc.AbilityUsable("Lightning Shield"),
        Inverse(sc.UnitHasAura("player", "Lightning Shield", "HELPFUL | PLAYER")),
    }
}

Triggers.Elemental.LavaBurst = {
    Name="Lava Burst",
    Binding=scripty.Bindings.LavaBurst,
    Conditions={
        sc.AbilityUsable("Lava Burst"),
        sc.AbilityInRange("Lava Burst", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Elemental.LightningBolt = {
    Name="Lightning Bolt",
    Binding=scripty.Bindings.LightningBolt,
    Conditions={
        sc.AbilityUsable("Lightning Bolt"),
        sc.AbilityInRange("Lightning Bolt", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Elemental.LightningBoltProc = {
    Name="Lightning Bolt",
    Binding=scripty.Bindings.LightningBolt,
    Conditions={
        sc.AbilityUsable("Lightning Bolt"),
        sc.AbilityInRange("Lightning Bolt", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Stormkeeper", "HELPFUL"),
    }
}

Triggers.Elemental.ChainLightningProc = {
    Name="Chain Lightning",
    Binding=scripty.Bindings.ChainLightning,
    Conditions={
        sc.AbilityUsable("Chain Lightning"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Stormkeeper", "HELPFUL"),
        sc.NumberOfEnemiesGreaterThan(1),
    }
}

Triggers.Elemental.ChainLightning = {
    Name="Chain Lightning",
    Binding=scripty.Bindings.ChainLightning,
    Conditions={
        sc.AbilityUsable("Chain Lightning"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
    }
}

Triggers.Elemental.Stormkeeper = {
    Name="Stormkeeper",
    Binding=scripty.Bindings.Stormkeeper,
    Conditions={
        sc.AbilityUsable("Stormkeeper"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Stormkeeper", "HELPFUL")),
    }
}

Triggers.Elemental.FlameShock = {
    Name="Flame Shock",
    Binding=scripty.Bindings.FlameShock,
    Conditions={
        sc.AbilityUsable("Flame Shock"),
        sc.AbilityInRange("Flame Shock", "target"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Flame Shock", 3, "PLAYER | HARMFUL"),
    }
}

Triggers.Elemental.EarthShock = {
    Name="Earth Shock",
    Binding=scripty.Bindings.EarthShock,
    Conditions={
        sc.AbilityUsable("Earth Shock"),
        sc.AbilityInRange("Earth Shock", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MAELSTROM, 60),
    }
}

Triggers.Elemental.EarthShockCap = {
    Name="Earth Shock",
    Binding=scripty.Bindings.EarthShock,
    Conditions={
        sc.AbilityUsable("Earth Shock"),
        sc.AbilityInRange("Earth Shock", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MAELSTROM, 100),
    }
}

Triggers.Elemental.EarthquakeProc = {
    Name="Earthquake",
    Binding=scripty.Bindings.Earthquake,
    Conditions={
        sc.AbilityUsable("Earthquake"),
        sc.AbilityInRange("Earthquake", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Echoes of Great Sundering", "HELPFUL"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MAELSTROM, 60),
    }
}

Triggers.Elemental.Earthquake = {
    Name="Earthquake",
    Binding=scripty.Bindings.Earthquake,
    Conditions={
        sc.AbilityUsable("Earthquake"),
        sc.AbilityInRange("Earthquake", "target"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MAELSTROM, 60),
    }
}

Triggers.Elemental.EarthquakeCap = {
    Name="Earthquake",
    Binding=scripty.Bindings.Earthquake,
    Conditions={
        sc.AbilityUsable("Earthquake"),
        sc.AbilityInRange("Earthquake", "target"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MAELSTROM, 100),
    }
}


Triggers.Elemental.SpiritwalkersGrace = {
    Name="Spiritwalker's Grace",
    Binding=scripty.Bindings.SpiritwalkersGrace,
    Conditions={
        sc.AbilityUsable("Spiritwalker's Grace"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Elemental.FireElemental = {
    Name="Fire Elemental",
    Binding=scripty.Bindings.FireElemental,
    Conditions={
        sc.AbilityUsable("Fire Elemental"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Fire Elemental", "HELPFUL")),
    }
}

--Enhance
Triggers.Enhance.SpiritWalk = {
    Name="Spirit Walk",
    Binding=scripty.Bindings.SpiritWalk,
    Conditions={
        sc.AbilityUsable("Spirit Walk"),
        sc.PlayerBeenMoving(),
        sc.UnitSpeedLessThan("player", 10),
        sc.AbilityOnCD("Wind Rush Totem"),
        sc.AbilityWasCastRecently("Wind Rush Totem", 10),
    }
}

Triggers.Enhance.SpiritWalk2 = {
    Name="Spirit Walk",
    Binding=scripty.Bindings.SpiritWalk,
    Conditions={
        sc.AbilityUsable("Spirit Walk"),
        sc.PlayerBeenMoving(),
        sc.UnitSpeedLessThan("player", 10),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.Enhance.CapacitorTotem = {
    Name="Capacitor Totem",
    Binding=scripty.Bindings.CapacitorTotem,
    Conditions={
        sc.AbilityUsable("Capacitor Totem"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.StunnableUnitCasting("target"),
    }
}

Triggers.Enhance.AncestralGuidance = {
    Name="Ancestral Guidance",
    Binding=scripty.Bindings.AncestralGuidance,
    Conditions={
        sc.AbilityUsable("Ancestral Guidance"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
    }
}

Triggers.Enhance.NaturesSwiftness = {
    Name="Nature's Swiftness",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.NaturesSwiftness,
    Conditions={
        sc.AbilityUsable("Nature's Swiftness"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.AbilityUsable("Chain Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        Inverse(sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL")),
    }
}

Triggers.Enhance.HealingSurgeMe = {
    Name="Healing Surge",
    Focus="player",
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.UnitHPLessThan("player", 65),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 40),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 5, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
    }
}
Triggers.Enhance.HealingSurgeSave = {
    Name="Healing Surge",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.AbilityInRange("Healing Surge", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 30),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 40),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 5, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
    }
}

Triggers.Enhance.LightningBoltMaelstromMiddle = {
    Name="Lightning Bolt",
    Binding=scripty.Bindings.LightningBolt,
    Conditions={
        sc.AbilityUsable("Lightning Bolt"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 5, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
    }
}

Triggers.Enhance.LightningBoltMaelstromMax = {
    Name="Lightning Bolt",
    Binding=scripty.Bindings.LightningBolt,
    Conditions={
        sc.AbilityUsable("Lightning Bolt"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 9, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
    }
}


Triggers.Enhance.ChainLightningMaelstromMiddle = {
    Name="Chain Lightning",
    Binding=scripty.Bindings.ChainLightning,
    Conditions={
        sc.AbilityUsable("Chain Lightning"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 5, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
    }
}

Triggers.Enhance.ChainLightningMaelstromMax = {
    Name="Chain Lightning",
    Binding=scripty.Bindings.ChainLightning,
    Conditions={
        sc.AbilityUsable("Chain Lightning"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 9, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
    }
}

Triggers.Enhance.FrostShockHailstorm = {
    Name="Frost Shock",
    Binding=scripty.Bindings.FrostShock,
    Conditions={
        sc.AbilityUsable("Frost Shock"),
        sc.AbilityInRange("Frost Shock", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Hailstorm", "HELPFUL"),
    }
}

Triggers.Enhance.LightningBoltPrimordial = {
    Name="Lightning Bolt",
    Binding=scripty.Bindings.LightningBolt,
    Conditions={
        sc.AbilityUsable("Lightning Bolt"),
        sc.AbilityInRange("Lightning Bolt", "target"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Maelstrom Weapon", 9, "HELPFUL")),
        sc.UnitHasAura("player", "Maelstrom Weapon", "HELPFUL"),
        sc.UnitHasAura("player", "Primordial Wave", "HELPFUL"),
    }
}

Triggers.Enhance.GreaterPurge = {
    Name="Greater Purge",
    Binding=scripty.Bindings.GreaterPurge,
    Conditions={
        sc.AbilityUsable("Greater Purge"),
        sc.AbilityInRange("Greater Purge", "target"),
        sc.UnitAttackable("target"),
        sc.UnitNeedsPurge("target"),
    }
}

Triggers.Enhance.LightningShield = {
    Name="Lightning Shield",
    Binding=scripty.Bindings.LightningShield,
    Conditions={
        sc.AbilityUsable("Lightning Shield"),
        Inverse(sc.UnitHasAura("player", "Lightning Shield", "HELPFUL")),
    }
}

Triggers.Enhance.FlametongueWeapon = {
    Name="Flametongue Weapon",
    Binding=scripty.Bindings.FlametongueWeapon,
    Conditions={
        sc.AbilityUsable("Flametongue Weapon"),
        Inverse(sc.OffHandEnchanted()),
    }
}

Triggers.Enhance.WindfuryWeapon = {
    Name="Windfury Weapon",
    Binding=scripty.Bindings.WindfuryWeapon,
    Conditions={
        sc.AbilityUsable("Windfury Weapon"),
        Inverse(sc.WeaponEnchanted()),
    }
}

Triggers.Enhance.Sundering = {
    Name="Sundering",
    Binding=scripty.Bindings.Sundering,
    Conditions={
        sc.AbilityUsable("Sundering"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
    }
}

Triggers.Enhance.CrashLightningBuff = {
    Name="Crash Lightning",
    Binding=scripty.Bindings.CrashLightning,
    Conditions={
        sc.AbilityUsable("Crash Lightning"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Crash Lightning", 2, "HELPFUL")),
        sc.UnitHasAura("player", "Crash Lightning", "HELPFUL"),
    }
}

Triggers.Enhance.LavaLashProc = {
    Name="Lava Lash",
    Binding=scripty.Bindings.LavaLash,
    Conditions={
        sc.AbilityUsable("Lava Lash"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("target", "Flame Shock", "HARMFUL | PLAYER"),
        sc.UnitHasAura("player", "Hot Hand", "HELPFUL"),
    }
}

Triggers.Enhance.LavaLashProc2 = {
    Name="Lava Lash",
    Binding=scripty.Bindings.LavaLash,
    Conditions={
        sc.AbilityUsable("Lava Lash"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("target", "Flame Shock", "HARMFUL | PLAYER"),
        Inverse(sc.UnitAuraCountLessThan("player", "Ashen Catalyst", 7, "HELPFUL")),
    }
}

Triggers.Enhance.LavaLash = {
    Name="Lava Lash",
    Binding=scripty.Bindings.LavaLash,
    Conditions={
        sc.AbilityUsable("Lava Lash"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Enhance.StormstrikeCrash = {
    Name="Stormstrike",
    Binding=scripty.Bindings.Stormstrike,
    Conditions={
        sc.AbilityUsable("Stormstrike"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
        sc.UnitAuraCountLessThan("player", "Crash Lightning", 2, "HELPFUL"),
        sc.UnitHasAura("player", "Crash Lightning", "HELPFUL"),
    }
}

Triggers.Enhance.Stormstrike = {
    Name="Stormstrike",
    Binding=scripty.Bindings.Stormstrike,
    Conditions={
        sc.AbilityUsable("Stormstrike"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Enhance.PrimordialWave = {
    Name="Primordial Wave",
    Binding=scripty.Bindings.PriomordialWaveAttack,
    Conditions={
        sc.AbilityUsable("Primordial Wave"),
        sc.AbilityInRange("Primordial Wave", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Enhance.IceStrike = {
    Name="Ice Strike",
    Binding=scripty.Bindings.IceStrike,
    Conditions={
        sc.AbilityUsable("Ice Strike"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Enhance.FeralSpirit = {
    Name="Feral Spirit",
    Binding=scripty.Bindings.FeralSpirit,
    Conditions={
        sc.AbilityUsable("Feral Spirit"),
        sc.AbilityInRange("Feral Spirit", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Enhance.CrashLightning = {
    Name="Crash Lightning",
    Binding=scripty.Bindings.CrashLightning,
    Conditions={
        sc.AbilityUsable("Crash Lightning"),
        sc.AbilityInRange("Lava Lash", "target"),
        sc.UnitAttackable("target"),
    }
}

--RestoShammy
Triggers.RestoShammy.Skyfury = {
    Name="Skyfury",
    Binding=scripty.Bindings.Skyfury,
    Conditions={
        sc.AbilityUsable("Skyfury"),
        sc.MarkNeeded(),
    }
}

Triggers.RestoShammy.HexGhost = {
    Name="Hex",
    Binding=scripty.Bindings.Hex,
    Conditions={
        sc.AbilityUsable("Hex"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
    }
}

Triggers.RestoShammy.FrostShockExplosives = {
    Name="Frost Shock",
    Binding=scripty.Bindings.FrostShock,
    Conditions={
        sc.AbilityUsable("Frost Shock"),
        sc.UnitNameIs("target", "Explosives"),
    }
}
Triggers.RestoShammy.ChainHeal = {
    Name="Chain Heal",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ChainHeal,
    Conditions={
        sc.AbilityUsable("Chain Heal"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 70),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 80),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        Inverse(sc.UnitHPLessThanUnit({name="bestHealTarget"}, {name="secondLowestHPUnit"}, 30)),
    }
}

Triggers.RestoShammy.ChainHealHighTide = {
    Name="Chain Heal",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ChainHeal,
    Conditions={
        sc.AbilityUsable("Chain Heal"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 90),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.UnitHasAura("player", "High Tide", "HELPFUL"),
        sc.UnitHasAura("player", "Tidebringer", "HELPFUL"),
        Inverse(sc.UnitHPLessThanUnit({name="bestHealTarget"}, {name="secondLowestHPUnit"}, 35)),
    }
}

Triggers.RestoShammy.ManaSpringTotem = {
    Name="Mana Spring Totem",
    Binding=scripty.Bindings.ManaSpringTotem,
    Conditions={
        sc.AbilityUsable("Mana Spring Totem"),
        Inverse(sc.UnitHasAura("player", "Ghost Wolf", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Mana Spring Totem", "HELPFUL")),
    }
}

Triggers.RestoShammy.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellRestoShammy(),
    }
}

Triggers.RestoShammy.PrimordialWave = {
    Name="Primordial Wave",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.PrimordialWave,
    Conditions={
        sc.AbilityUsable("Primordial Wave"),
        sc.AbilityInRange("Primordial Wave", {name="bestHealTarget"}),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Riptide", "PLAYER | HELPFUL")),
    }
}

Triggers.RestoShammy.PrimordialWaveNoTidal = {
    Name="Primordial Wave",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.PrimordialWave,
    Conditions={
        sc.AbilityUsable("Primordial Wave"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        Inverse(sc.UnitHasAura("player", "Tidal Waves", "PLAYER | HELPFUL")),
        sc.AbilityInRange("Primordial Wave", {name="bestHealTarget"}),
    }
}

Triggers.RestoShammy.AncestralGuidance = {
    Name="Ancestral Guidance",
    Binding=scripty.Bindings.AncestralGuidance,
    Conditions={
        sc.AbilityUsable("Ancestral Guidance"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.RestoShammy.EverRisingTide = {
    Name="Ever-Rising Tide",
    Binding=scripty.Bindings.EverRisingTide,
    Conditions={
        sc.AbilityUsable("Ever-Rising Tide"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.EverRisingTide2 = {
    Name="Ever-Rising Tide",
    Binding=scripty.Bindings.EverRisingTide,
    Conditions={
        sc.AbilityUsable("Ever-Rising Tide"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.UnleashLife = {
    Name="Unleash Life",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.UnleashLife,
    Conditions={
        sc.AbilityUsable("Unleash Life"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityInRange("Unleash Life", {name="bestHealTarget"}),
    }
}

Triggers.RestoShammy.RiptideNoBuff = {
    Name="Riptide",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Riptide,
    Conditions={
        sc.AbilityUsable("Riptide"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Riptide", "PLAYER | HELPFUL")),
        sc.AbilityInRange("Riptide", {name="bestHealTarget"}),
    }
}

Triggers.RestoShammy.Riptide = {
    Name="Riptide",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Riptide,
    Conditions={
        sc.AbilityUsable("Riptide"),
        sc.AbilityInRange("Riptide", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.RestoShammy.RiptideExtra = {
    Name="Riptide",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Riptide,
    Conditions={
        sc.AbilityUsable("Riptide"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        sc.AbilityInRange("Riptide", {name="bestHealTarget"}),
        sc.SpellChargesGreaterThan("Riptide", 1),
    }
}

Triggers.RestoShammy.WaterShield = {
    Name="Water Shield",
    Binding=scripty.Bindings.WaterShield,
    Conditions={
        sc.AbilityUsable("Water Shield"),
        Inverse(sc.UnitHasAura("player", "Water Shield", "HELPFUL | PLAYER")),
    }
}

Triggers.RestoShammy.SpiritLinkTotem = {
    Name="Spirit Link Totem",
    Binding=scripty.Bindings.SpiritLinkTotem,
    Conditions={
        sc.AbilityUsable("Spirit Link Totem"),
        sc.SpiritLinkTime(),
    }
}

Triggers.RestoShammy.PurifySpirit = {
    Name="Purify Spirit",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.PurifySpirit,
    Conditions={
        sc.AbilityUsable("Purify Spirit"),
        sc.AbilityInRange("Purify Spirit", {name="purifyTarget"}),
        Inverse(sc.PlayerIsInPvPZone()),
    }
}

Triggers.RestoShammy.HealingWave = {
    Name="Healing Wave",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingWave,
    Conditions={
        sc.AbilityUsable("Healing Wave"),
        sc.AbilityInRange("Healing Wave", {name="bestHealTarget"}),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 10),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
    }
}

Triggers.RestoShammy.HealingSurgeSpend = {
    Name="Healing Surge",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.AbilityInRange("Healing Wave", {name="bestHealTarget"}),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        sc.UnitHasAura("player", "Tidal Waves", "PLAYER | HELPFUL"),
    }
}

Triggers.RestoShammy.HealingWavePrimordial = {
    Name="Healing Wave",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingWave,
    Conditions={
        sc.AbilityUsable("Healing Wave"),
        sc.AbilityInRange("Healing Wave", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        sc.UnitHasAura("player", "Primordial Wave", "HELPFUL | PLAYER"),        }
}

Triggers.RestoShammy.HealingSurge = {
    Name="Healing Surge",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.AbilityInRange("Healing Surge", {name="bestHealTarget"}),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 60),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.HealingSurgeInstant = {
    Name="Healing Surge",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.UnitHasAura("player", "Spiritwalker's Tidal Totem", "HELPFUL"),
        sc.AbilityInRange("Healing Surge", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
    }
}

Triggers.RestoShammy.HealingSurgeInstant2 = {
    Name="Healing Surge",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.UnitHasAura("player", "Ancestral Swiftness", "HELPFUL"),
        sc.AbilityInRange("Healing Surge", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
    }
}


Triggers.RestoShammy.ChainHealBingo = {
    Name="Chain Heal",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ChainHeal,
    Conditions={
        sc.AbilityUsable("Chain Heal"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 90),
        sc.UnitHasAura("player", "Tidebringer", "HELPFUL"),
        sc.UnitHasAura("player", "Tidal Waves", "PLAYER | HELPFUL"),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 20),
        Inverse(sc.UnitHPLessThanUnit({name="bestHealTarget"}, {name="secondLowestHPUnit"}, 40)),
    }
}

Triggers.RestoShammy.ChainHealInstant = {
    Name="Chain Heal",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ChainHeal,
    Conditions={
        sc.AbilityUsable("Chain Heal"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 90),
        sc.UnitHasAura("player", "Ancestral Swiftness", "PLAYER | HELPFUL"),
    }
}

Triggers.RestoShammy.ChainHealTidal = {
    Name="Chain Heal",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ChainHeal,
    Conditions={
        sc.AbilityUsable("Chain Heal"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 75),
        sc.UnitHasAura("player", "Tidal Waves", "PLAYER | HELPFUL"),
    }
}

Triggers.RestoShammy.ChainHealSpend = {
    Name="Chain Heal",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ChainHeal,
    Conditions={
        sc.AbilityUsable("Chain Heal"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 85),
        sc.UnitHasAura("player", "Tidal Waves", "PLAYER | HELPFUL"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.Wellspring = {
    Name="Wellspring",
    Binding=scripty.Bindings.Wellspring,
    Conditions={
        sc.AbilityUsable("Wellspring"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 85),
    }
}

Triggers.RestoShammy.HealingSurgeSave = {
    Name="Healing Surge",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HealingSurge,
    Conditions={
        sc.AbilityUsable("Healing Surge"),
        sc.AbilityInRange("Healing Surge", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 60),
    }
}

Triggers.RestoShammy.GhostWolf = {
    Name="Ghost Wolf",
    Binding=scripty.Bindings.GhostWolf,
    Conditions={
        sc.AbilityUsable("Ghost Wolf"),
        Inverse(sc.UnitHasAura("player", "Ghost Wolf", "HELPFUL")),
        sc.PlayerBeenMoving(),
    }
}

Triggers.RestoShammy.ManaTideTotem = {
    Name="Mana Tide Totem",
    Binding=scripty.Bindings.ManaTideTotem,
    Conditions={
        sc.AbilityUsable("Mana Tide Totem"),
        Inverse(sc.UnitHasAura("player", "Ascendance", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
    }
}

Triggers.RestoShammy.EarthShield = {
    Name="Earth Shield",
    Focus={name="bestESTarget"},
    Binding=scripty.Bindings.EarthShield,
    Conditions={
        sc.AbilityUsable("Earth Shield"),
        sc.AbilityInRange("Earth Shield", {name="bestESTarget"}),
    }
}

Triggers.RestoShammy.EarthShieldMissing = {
    Name="Earth Shield",
    Focus={name="bestESTarget"},
    Binding=scripty.Bindings.EarthShield,
    Conditions={
        sc.AbilityUsable("Earth Shield"),
        sc.AbilityInRange("Earth Shield", {name="bestESTarget"}),
        Inverse(sc.UnitHasAura({name="bestESTarget"}, "Earth Shield", "PLAYER | HELPFUL")),
    }
}

Triggers.RestoShammy.EarthlivingWeapon = {
    Name="Earth Shield",
    Focus="player",
    Binding=scripty.Bindings.EarthlivingWeapon,
    Conditions={
        sc.AbilityUsable("Earthliving Weapon"),
        sc.EarthLivingNeeded(),
    }
}

Triggers.RestoShammy.HealingStreamTotem = {
    Name="Healing Stream Totem",
    Binding=scripty.Bindings.HealingStreamTotem,
    Conditions={
        sc.AbilityUsable("Healing Stream Totem"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
        Inverse(sc.AbilityWasCastRecently("Healing Stream Totem", 15)),
    }
}

Triggers.RestoShammy.HealingTideTotem = {
    Name="Healing Tide Totem",
    Binding=scripty.Bindings.HealingTideTotem,
    Conditions={
        sc.AbilityUsable("Healing Tide Totem"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 90),
    }
}

Triggers.RestoShammy.WindRushTotem = {
    Name="Wind Rush Totem",
    Binding=scripty.Bindings.WindRushTotem,
    Conditions={
        sc.AbilityUsable("Wind Rush Totem"),
        sc.PlayerBeenMoving(),
        Inverse(sc.IsUnitInCombat("player")),
    }
}

Triggers.RestoShammy.Ascendance = {
    Name="Ascendance",
    Binding=scripty.Bindings.Ascendance,
    Conditions={
        sc.AbilityUsable("Ascendance"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        Inverse(sc.UnitHasAura("player", "Ascendance", "HELPFUL")),
    }
}

Triggers.RestoShammy.HealingRainAoE = {
    Name="Healing Rain",
    Binding=scripty.Bindings.HealingRain,
    Conditions={
        sc.AbilityUsable("Healing Rain"),
        sc.InjuredNearbyGreaterThan(2),
        Inverse(sc.UnitHPLessThan({name="bestHealTarget"}, 40)),
    }
}

Triggers.RestoShammy.HealingRainSingle = {
    Name="Healing Rain",
    Binding=scripty.Bindings.HealingRain,
    Conditions={
        sc.AbilityUsable("Healing Rain"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.RestoShammy.Downpour = {
    Name="Downpour",
    Binding=scripty.Bindings.Downpour,
    Conditions={
        sc.AbilityUsable("Downpour"),
        sc.UnitHPLessThan("player", 90),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 90),
    }
}

Triggers.RestoShammy.DownpourSingle = {
    Name="Downpour",
    Binding=scripty.Bindings.Downpour,
    Conditions={
        sc.AbilityUsable("Downpour"),
        sc.InjuredNearbyGreaterThan(0),
        Inverse(sc.UnitHPLessThan({name="bestHealTarget"}, 40)),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.AncestralSwiftness = {
    Name="Ancestral Swiftness",
    Binding=scripty.Bindings.AncestralSwiftness,
    Conditions={
        sc.AbilityUsable("Ancestral Swiftness"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.AbilityUsable("Chain Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 60),
    }
}

Triggers.RestoShammy.AncestralSwiftness2 = {
    Name="Ancestral Swiftness",
    Binding=scripty.Bindings.AncestralSwiftness,
    Conditions={
        sc.AbilityUsable("Ancestral Swiftness"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.AbilityUsable("Chain Heal"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 90),
    }
}

Triggers.RestoShammy.NaturesSwiftness = {
    Name="Nature's Swiftness",
    Binding=scripty.Bindings.NaturesSwiftness,
    Conditions={
        sc.AbilityUsable("Nature's Swiftness"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.AbilityUsable("Chain Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 60),
    }
}

Triggers.RestoShammy.NaturesSwiftness2 = {
    Name="Nature's Swiftness",
    Binding=scripty.Bindings.NaturesSwiftness,
    Conditions={
        sc.AbilityUsable("Nature's Swiftness"),
        sc.AbilityInRange("Chain Heal", {name="bestHealTarget"}),
        sc.AbilityUsable("Chain Heal"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 90),
    }
}

Triggers.RestoShammy.SpiritwalkersGrace = {
    Name="Spiritwalker's Grace",
    Binding=scripty.Bindings.SpiritwalkersGrace,
    Conditions={
        sc.AbilityUsable("Spiritwalker's Grace"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
    }
}

Triggers.RestoShammy.WindShear = {
    Name="Wind Shear",
    Binding=scripty.Bindings.WindShear,
    Conditions={
        sc.AbilityUsable("Wind Shear"),
        sc.AbilityInRange("Wind Shear", "target"),
        sc.UnitAttackable("target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.RestoShammy.Thunderstorm = {
    Name="Thunderstorm",
    Binding=scripty.Bindings.Thunderstorm,
    Conditions={
        sc.AbilityUsable("Thunderstorm"),
        sc.StunnableUnitCasting("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.RestoShammy.EarthbindTotem = {
    Name="Earthbind Totem",
    Binding=scripty.Bindings.EarthbindTotem,
    Conditions={
        function() return false end,
    }
}

Triggers.RestoShammy.GustOfWind = {
    Name="Gust of Wind",
    Binding=scripty.Bindings.GustOfWind,
    Conditions={
        function() return false end,
    }
}

Triggers.BM.FreezingTrap = {
    Name="Freezing Trap",
    Binding=scripty.Bindings.FreezingTrap,
    Conditions={
        function() return false end,
    }
}


Triggers.RestoShammy.ChainLightning = {
    Name="Chain Lightning",
    Binding=scripty.Bindings.ChainLightning,
    Conditions={
        sc.AbilityUsable("Chain Lightning"),
        sc.AbilityInRange("Chain Lightning", "target"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.LavaBurst = {
    Name="Lava Burst",
    Binding=scripty.Bindings.LavaBurst,
    Conditions={
        sc.AbilityUsable("Lava Burst"),
        sc.AbilityInRange("Lava Burst", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.LavaBurstInstant = {
    Name="Lava Burst",
    Binding=scripty.Bindings.LavaBurst,
    Conditions={
        sc.AbilityUsable("Lava Burst"),
        sc.AbilityInRange("Lava Burst", "target"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Lava Surge", "HELPFUL"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.LightningBolt = {
    Name="Lightning Bolt",
    Binding=scripty.Bindings.LightningBolt,
    Conditions={
        sc.AbilityUsable("Lightning Bolt"),
        sc.AbilityInRange("Lightning Bolt", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.FrostShock = {
    Name="Frost Shock",
    Binding=scripty.Bindings.FrostShock,
    Conditions={
        sc.AbilityUsable("Frost Shock"),
        sc.AbilityInRange("Frost Shock", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.LightningLasso = {
    Name="Lightning Lasso",
    Binding=scripty.Bindings.LightningLasso,
    Conditions={
        sc.AbilityUsable("Lightning Lasso"),
        sc.AbilityInRange("Lightning Lasso", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.RestoShammy.FlameShock = {
    Name="Flame Shock",
    Binding=scripty.Bindings.FlameShock,
    Conditions={
        sc.AbilityUsable("Flame Shock"),
        sc.AbilityInRange("Flame Shock", "target"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Flame Shock", 3, "PLAYER | HARMFUL"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.RestoShammy.EarthElemental = {
    Name="Earth Elemental",
    Binding=scripty.Bindings.EarthElemental,
    Conditions={
        function() return false end,
    }
}

--Devastation
Triggers.Devastation.AzureStrike = {
    Name="Azure Strike",
    Binding=scripty.Bindings.AzureStrike,
    Conditions={
        sc.AbilityUsable("Azure Strike"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}
Triggers.Devastation.AzureStrikeSingle = {
    Name="Azure Strike",
    Binding=scripty.Bindings.AzureStrike,
    Conditions={
        sc.AbilityUsable("Azure Strike"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}
Triggers.Devastation.Disintegrate = {
    Name="Disintegrate",
    Binding=scripty.Bindings.Disintegrate,
    Conditions={
        sc.AbilityUsable("Disintegrate"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}
Triggers.Devastation.FireBreath = {
    Name="Fire Breath",
    Binding=scripty.Bindings.FireBreath,
    Conditions={
        sc.AbilityUsable("Fire Breath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}

Triggers.Devastation.LivingFlame = {
    Name="Living Flame",
    Binding=scripty.Bindings.LivingFlame,
    Conditions={
        sc.AbilityUsable("Living Flame"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}

Triggers.Devastation.LivingFlameLeaping = {
    Name="Living Flame",
    Binding=scripty.Bindings.LivingFlame,
    Conditions={
        sc.AbilityUsable("Living Flame"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
        sc.UnitHasAura("player", "Leaping Flames", "HELPFUL"),
    }
}

Triggers.Devastation.EmeraldBlossom = {
    Name="Emerald Blossom",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.EmeraldBlossom,
    Conditions={
        sc.AbilityUsable("Emerald Blossom"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.AbilityInRange("Living Flame", {name="bestHealTarget"}),
    }
}

Triggers.Devastation.EmeraldBlossomMe = {
    Name="Emerald Blossom",
    Focus="player",
    Binding=scripty.Bindings.EmeraldBlossom,
    Conditions={
        sc.AbilityUsable("Emerald Blossom"),
        sc.UnitHPLessThan("player", 70),
    }
}

Triggers.Devastation.BlessingOfTheBronze = {
    Name="Blessing of the Bronze",
    Focus="player",
    Binding=scripty.Bindings.BlessingOfTheBronze,
    Conditions={
        sc.AbilityUsable("Blessing of the Bronze"),
        sc.BronzeNeeded(),
    }
}

Triggers.Devastation.LivingFlameHeal = {
    Name="Living Flame",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LivingFlameHeal,
    Conditions={
        sc.AbilityUsable("Living Flame"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.AbilityInRange("Living Flame", {name="bestHealTarget"}),
    }
}

Triggers.Devastation.LivingFlameHealMe = {
    Name="Living Flame",
    Focus="player",
    Binding=scripty.Bindings.LivingFlameHeal,
    Conditions={
        sc.AbilityUsable("Living Flame"),
        sc.UnitHPLessThan("player", 50),
    }
}

Triggers.Devastation.VerdantEmbrace = {
    Name="Verdant Embrace",
    Focus="player",
    Binding=scripty.Bindings.VerdantEmbrace,
    Conditions={
        sc.AbilityUsable("Verdant Embrace"),
        sc.UnitHPLessThan("player", 80),
    }
}

Triggers.Devastation.RenewingBlaze = {
    Name="Renewing Blaze",
    Binding=scripty.Bindings.RenewingBlaze,
    Conditions={
        sc.AbilityUsable("Renewing Blaze"),
        sc.UnitHPLessThan("player", 75),
        sc.UnitHPDamageAtLeastTakenSince("player", 50, 5),
    }
}

Triggers.Devastation.SourceOfMagic = {
    Name="Source of Magic",
    Focus={name="bestSOMTarget"},
    Binding=scripty.Bindings.SourceOfMagic,
    Conditions={
        sc.AbilityUsable("Source of Magic"),
        Inverse(sc.UnitHasAura({name="bestSOMTarget"}, "Source of Magic", "PLAYER | HELPFUL")),
    }
}

Triggers.Devastation.Dragonrage = {
    Name="Dragonrage",
    Binding=scripty.Bindings.Dragonrage,
    Conditions={
        sc.AbilityUsable("Dragonrage"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Devastation.TipTheScales = {
    Name="Tip the Scales",
    Binding=scripty.Bindings.TipTheScales,
    Conditions={
        sc.AbilityUsable("Tip the Scales"),
        Inverse(sc.UnitHasAura("player", "Tip the Scales", "PLAYER | HELPFUL")),
    }
}

Triggers.Devastation.EternitySurge = {
    Name="Eternity Surge",
    Binding=scripty.Bindings.EternitySurge,
    Conditions={
        sc.AbilityUsable("Eternity Surge"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}

Triggers.Devastation.Pyre = {
    Name="Pyre",
    Binding=scripty.Bindings.Pyre,
    Conditions={
        sc.AbilityUsable("Pyre"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(2),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}

Triggers.Devastation.ShatteringStar = {
    Name="Shattering Star",
    Binding=scripty.Bindings.ShatteringStar,
    Conditions={
        sc.AbilityUsable("Shattering Star"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Azure Strike", "target"),
    }
}

Triggers.Devastation.CauterizingFlame = {
    Name="Cauterizing Flame",
    Focus={name="maxPurifyTarget"},
    Binding=scripty.Bindings.CauterizingFlame,
    Conditions={
        sc.AbilityUsable("Cauterizing Flame"),
    }
}

Triggers.Devastation.Expunge = {
    Name="Expunge",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.Expunge,
    Conditions={
        sc.AbilityUsable("Expunge"),
    }
}

Triggers.Devastation.Quell = {
    Name="Quell",
    Binding=scripty.Bindings.Quell,
    Conditions={
        sc.AbilityUsable("Quell"),
        sc.TargetIsReadyForInterrupt(),
        sc.AbilityInRange("Quell", "target"),
    }
}

Triggers.Devastation.DeepBreath = {
    Name="Deep Breath",
    Binding=scripty.Bindings.DeepBreath,
    Conditions={
        function() return false end,
    }
}

Triggers.Devastation.Hover = {
    Name="Hover",
    Binding=scripty.Bindings.Hover,
    Conditions={
        function() return false end,
    }
}

Triggers.Devastation.Landslide = {
    Name="Landslide",
    Binding=scripty.Bindings.Landslide,
    Conditions={
        function() return false end,
    }
}


--Outlaw
Triggers.Outlaw.RogueSetSingle = {
    Name="Rogue Set Single",
    Binding=scripty.Bindings.RogueSetSingle,
    Conditions={
        sc.SpecAndSetShouldBe("single"),
        Inverse(sc.IsUnitInCombat("player")),
    }
}
Triggers.Outlaw.RogueSetPvp = {
    Name="Rogue Set PvP",
    Binding=scripty.Bindings.RogueSetPvp,
    Conditions={
        sc.SpecAndSetShouldBe("pvp"),
        Inverse(sc.IsUnitInCombat("player")),
    }
}

Triggers.Outlaw.Vanish = {
    Name="Vanish",
    Binding=scripty.Bindings.Vanish,
    Conditions={
        sc.AbilityUsable("Vanish"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.StealthAbilitiesUsable()),
        sc.UnitHasAura("player", "Adrenaline Rush", "HELPFUL"),
        sc.AbilityUsable("Between the Eyes"),
        Inverse(sc.VanishWillEndCombat()),
    }
}
Triggers.Outlaw.ShadowDance = {
    Name="Shadow Dance",
    Binding=scripty.Bindings.ShadowDance,
    Conditions={
        sc.AbilityUsable("Shadow Dance"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.StealthAbilitiesUsable()),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 6),
        sc.AbilityUsable("Between the Eyes"),
    }
}
Triggers.Outlaw.TricksOfTheTrade = {
    Name="TricksOfTheTrade",
    Focus={name="MDTarget"},
    Binding=scripty.Bindings.TricksOfTheTrade,
    Conditions={
        sc.AbilityUsable("Tricks of the Trade"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}
Triggers.Outlaw.Dreadblades = {
    Name="Dreadblades",
    Binding=scripty.Bindings.Dreadblades,
    Conditions={
        sc.AbilityUsable("Dreadblades"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}
Triggers.Outlaw.RollTheBones = {
    Name="Roll the Bones",
    Binding=scripty.Bindings.RollTheBones,
    Conditions={
        sc.AbilityUsable("Roll the Bones"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
        sc.ShouldRerollBuffs(),
    }
}
Triggers.Outlaw.PistolShotOpportunity = {
    Name="Pistol Shot",
    Binding=scripty.Bindings.PistolShot,
    Conditions={
        sc.AbilityUsable("Pistol Shot"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
        sc.UnitHasAura("player", "Opportunity", "HELPFUL"),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
    }
}
Triggers.Outlaw.PistolShot = {
    Name="Pistol Shot",
    Binding=scripty.Bindings.PistolShot,
    Conditions={
        sc.AbilityUsable("Pistol Shot"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 140),
    }
}
Triggers.Outlaw.KeepItRolling = {
    Name="Keep it Rolling",
    Binding=scripty.Bindings.KeepItRolling,
    Conditions={
        sc.AbilityUsable("Keep it Rolling"),
        sc.ShouldKeepBuffs(),
    }
}
Triggers.Outlaw.GhostlyStrike = {
    Name="Ghostly Strike",
    Binding=scripty.Bindings.GhostlyStrike,
    Conditions={
        sc.AbilityUsable("Ghostly Strike"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 7)),
    }
}
Triggers.Outlaw.Dispatch = {
    Name="Dispatch",
    Binding=scripty.Bindings.Dispatch,
    Conditions={
        sc.AbilityUsable("Dispatch"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 6),
    }
}
Triggers.Outlaw.BladeFlurry = {
    Name="Blade Flurry",
    Binding=scripty.Bindings.BladeFlurry,
    Conditions={
        sc.AbilityUsable("Blade Flurry"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.UnitHasAura("player", "Blade Flurry", "HELPFUL")),
    }
}
Triggers.Outlaw.BetweenTheEyes = {
    Name="Between the Eyes",
    Binding=scripty.Bindings.BetweenTheEyes,
    Conditions={
        sc.AbilityUsable("Between the Eyes"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Between the Eyes", "target"),
        -- Inverse(sc.AbilityCDLessThan("Vanish", 45)),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 6),
    }
}
Triggers.Outlaw.BetweenTheEyesStealth = {
    Name="Between the Eyes",
    Binding=scripty.Bindings.BetweenTheEyes,
    Conditions={
        sc.AbilityUsable("Between the Eyes"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Between the Eyes", "target"),
        sc.StealthAbilitiesUsable(),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 5),
    }
}
Triggers.Outlaw.AdrenalineRush = {
    Name="Adrenaline Rush",
    Binding=scripty.Bindings.AdrenalineRush,
    Conditions={
        sc.AbilityUsable("Adrenaline Rush"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 3)),
    }
}
Triggers.Outlaw.ThistleTea = {
    Name="Thistle Tea",
    Binding=scripty.Bindings.ThistleTea,
    Conditions={
        sc.AbilityUsable("Thistle Tea"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 30)),
    }
}
Triggers.Outlaw.SinisterStrike = {
    Name="Sinister Strike",
    Binding=scripty.Bindings.SinisterStrike,
    Conditions={
        sc.AbilityUsable("Sinister Strike"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}
Triggers.Outlaw.Ambush = {
    Name="Ambush",
    Binding=scripty.Bindings.Ambush,
    Conditions={
        sc.AbilityUsable("Ambush"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.StealthAbilitiesUsable(),
    }
}
Triggers.Outlaw.AmbushProc = {
    Name="Ambush",
    Binding=scripty.Bindings.Ambush,
    Conditions={
        sc.AbilityUsable("Ambush"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Audacity", "HELPFUL"),
    }
}

--Assass
Triggers.Assass.DeadlyPoison = {
    Name="Deadly Poison",
    Binding=scripty.Bindings.DeadlyPoison,
    Conditions={
        sc.AbilityUsable("Deadly Poison"),
        Inverse(sc.UnitHasAuraOrSpell("player", "Deadly Poison", "HELPFUL")),
    }
}
Triggers.Assass.ThistleTea = {
    Name="Thistle Tea",
    Binding=scripty.Bindings.ThistleTea,
    Conditions={
        sc.AbilityUsable("Thistle Tea"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 100)),
    }
}
Triggers.Assass.Vanish = {
    Name="Vanish",
    Binding=scripty.Bindings.Vanish,
    Conditions={
        sc.AbilityUsable("Vanish"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.StealthAbilitiesUsable()),
        sc.AbilityUsable("Garrote"),
        sc.UnitAuraDurationLessThan("target", "Garrote", 3, "PLAYER | HARMFUL"),
        Inverse(sc.AbilityCDLessThan("Shadow Dance", 5)),
    }
}

Triggers.Assass.ShadowDance = {
    Name="Shadow Dance",
    Binding=scripty.Bindings.ShadowDance,
    Conditions={
        sc.AbilityUsable("Shadow Dance"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.StealthAbilitiesUsable()),
        sc.AbilityUsable("Garrote"),
        sc.UnitAuraDurationLessThan("target", "Garrote", 3, "PLAYER | HARMFUL"),
    }
}

Triggers.Assass.Garrote = {
    Name="Garrote",
    Binding=scripty.Bindings.Garrote,
    Conditions={
        sc.AbilityUsable("Garrote"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAuraDurationLessThan("target", "Garrote", 3, "PLAYER | HARMFUL"),
    }
}

Triggers.Assass.StealthGarrote = {
    Name="Stealth Garrote",
    Binding=scripty.Bindings.Garrote,
    Conditions={
        sc.AbilityUsable("Garrote"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAuraDurationLessThan("target", "Garrote", 3, "PLAYER | HARMFUL"),
        sc.StealthAbilitiesUsable(),
    }
}

Triggers.Assass.Rupture = {
    Name="Rupture",
    Binding=scripty.Bindings.Rupture,
    Conditions={
        sc.AbilityUsable("Rupture"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAuraDurationLessThan("target", "Rupture", 3, "PLAYER | HARMFUL"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 4),
    }
}

Triggers.Assass.SliceAndDice = {
    Name="Slice and Dice",
    Binding=scripty.Bindings.SliceAndDice,
    Conditions={
        sc.AbilityUsable("Slice and Dice"),
        Inverse(sc.UnitHasAura("player", "Slice and Dice", "HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 1),
    }
}

Triggers.Assass.FanOfKnives = {
    Name="Fan of Knives",
    Binding=scripty.Bindings.FanOfKnives,
    Conditions={
        sc.AbilityUsable("Fan of Knives"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 100),
        sc.NumberOf10YardEnemiesGreaterThan(2),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
    }
}

Triggers.Assass.CrimsonTempest = {
    Name="Crimson Tempest",
    Binding=scripty.Bindings.CrimsonTempest,
    Conditions={
        sc.AbilityUsable("Crimson Tempest"),
        sc.NumberOf10YardEnemiesGreaterThan(1),
        sc.UnitAuraDurationLessThan("target", "Crimson Tempest", 3, "PLAYER | HARMFUL"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 4),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
    }
}

Triggers.Assass.Envenom = {
    Name="Envenom",
    Binding=scripty.Bindings.Envenom,
    Conditions={
        sc.AbilityUsable("Envenom"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 4),
    }
}

Triggers.Assass.SerratedBoneSpike = {
    Name="Serrated Bone Spike",
    Binding=scripty.Bindings.SerratedBoneSpike,
    Conditions={
        sc.AbilityUsable("Serrated Bone Spike"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Serrated Bone Spike", "target"),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Vanish", "HELPFUL")),
    }
}

Triggers.Assass.Mutilate = {
    Name="Mutilate",
    Binding=scripty.Bindings.Mutilate,
    Conditions={
        sc.AbilityUsable("Mutilate"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 100),
    }
}

Triggers.Assass.Ambush = {
    Name="Ambush",
    Binding=scripty.Bindings.Ambush,
    Conditions={
        sc.AbilityUsable("Ambush"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.StealthAbilitiesUsable(),
    }
}

Triggers.Assass.Deathmark = {
    Name="Deathmark",
    Binding=scripty.Bindings.Deathmark,
    Conditions={
        sc.AbilityUsable("Deathmark"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Assass.Shiv = {
    Name="Shiv",
    Binding=scripty.Bindings.Shiv,
    Conditions={
        sc.AbilityUsable("Shiv"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.AbilityWasCastRecently("Shiv", 8)),
    }
}

--Subtlety
Triggers.Subtlety.SymbolsOfDeath = {
    Name="Symbols of Death",
    Binding=scripty.Bindings.SymbolsOfDeath,
    Conditions={
        sc.AbilityUsable("Symbols of Death"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 40)),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.SecretTechnique = {
    Name="Secret Technique",
    Binding=scripty.Bindings.SecretTechnique,
    Conditions={
        sc.AbilityUsable("Secret Technique"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 6),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Rupture = {
    Name="Rupture",
    Binding=scripty.Bindings.Rupture,
    Conditions={
        sc.AbilityUsable("Rupture"),
        Inverse(sc.UnitHasAura("target", "Rupture", "PLAYER | HARMFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 6),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Gloomblade = {
    Name="Gloomblade",
    Binding=scripty.Bindings.Gloomblade,
    Conditions={
        sc.AbilityUsable("Gloomblade"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Flagellation = {
    Name="Flagellation",
    Binding=scripty.Bindings.Flagellation,
    Conditions={
        sc.AbilityUsable("Flagellation"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 5),
    }
}

Triggers.Subtlety.ShurikenToss = {
    Name="Shuriken Toss",
    Binding=scripty.Bindings.ShurikenToss,
    Conditions={
        sc.AbilityUsable("Shuriken Toss"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
    }
}

Triggers.Subtlety.ShurikenTornado = {
    Name="Shuriken Tornado",
    Binding=scripty.Bindings.ShurikenTornado,
    Conditions={
        sc.AbilityUsable("Shuriken Tornado"),
        sc.NumberOf10YardEnemiesGreaterThan(1),
    }
}

Triggers.Subtlety.ShurikenStorm = {
    Name="Shuriken Storm",
    Binding=scripty.Bindings.ShurikenStorm,
    Conditions={
        sc.AbilityUsable("Shuriken Storm"),
        sc.NumberOf10YardEnemiesGreaterThan(1),
    }
}

Triggers.Subtlety.BlackPowder = {
    Name="Black Powder",
    Binding=scripty.Bindings.BlackPowder,
    Conditions={
        sc.AbilityUsable("Black Powder"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 5),
        sc.NumberOf10YardEnemiesGreaterThan(1),
    }
}

Triggers.Subtlety.Vanish2 = {
    Name="Vanish",
    Binding=scripty.Bindings.Vanish,
    Conditions={
        sc.AbilityUsable("Vanish"),
        sc.UnitHPLessThan("player", 65),
        Inverse(sc.UnitIsUnit("player", "targettarget")),
        Inverse(sc.UnitIsPlayer("target")),
    }
}

Triggers.Subtlety.Vanish = {
    Name="Vanish",
    Binding=scripty.Bindings.Vanish,
    Conditions={
        sc.AbilityUsable("Vanish"),
        sc.UnitHPLessThan("player", 65),
        sc.UnitIsPlayer("target"),
    }
}

Triggers.Subtlety.Stealth = {
    Name="Stealth",
    Binding=scripty.Bindings.Stealth,
    Conditions={
        sc.AbilityUsable("Stealth"),
    }
}

Triggers.Subtlety.Sprint = {
    Name="Sprint",
    Binding=scripty.Bindings.Sprint,
    Conditions={
        sc.AbilityUsable("Sprint"),
        sc.UnitSpeedLessThan("player", 13),
        sc.PlayerBeenMoving(),
    }
}

Triggers.Subtlety.SliceAndDice = {
    Name="Slice and Dice",
    Binding=scripty.Bindings.SliceAndDice,
    Conditions={
        sc.AbilityUsable("Slice and Dice"),
        Inverse(sc.UnitHasAura("player", "Slice and Dice", "HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 4),
    }
}

Triggers.Subtlety.ShadowDance = {
    Name="Shadow Dance",
    Binding=scripty.Bindings.ShadowDance,
    Conditions={
        sc.AbilityUsable("Shadow Dance"),
        Inverse(sc.UnitHasAura("player", "Stealth", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Shadow Dance", "HELPFUL")),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Sepsis = {
    Name="Sepsis",
    Binding=scripty.Bindings.Sepsis,
    Conditions={
        sc.AbilityUsable("Sepsis"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Sap = {
    Name="Sap",
    Binding=scripty.Bindings.Sap,
    Conditions={
        sc.AbilityUsable("Sap"),
        sc.UnitIsPlayer("target"),
        sc.EnemyUnitWithin("target", 10),
        Inverse(sc.IsUnitInCombat("target")),
        Inverse(sc.UnitHasAura("target", "Sap", "HARMFUL")),
        Inverse(sc.AbilityWasCastRecently("Sap", 1)),
    }
}

Triggers.Subtlety.CripplingPoison = {
    Name="Crippling Poison",
    Binding=scripty.Bindings.CripplingPoison,
    Conditions={
        sc.AbilityUsable("Crippling Poison"),
        Inverse(sc.UnitHasAuraOrSpell("player", "Crippling Poison", "HELPFUL")),
    }
}

Triggers.Subtlety.InstantPoison = {
    Name="Instant Poison",
    Binding=scripty.Bindings.InstantPoison,
    Conditions={
        sc.AbilityUsable("Instant Poison"),
        Inverse(sc.UnitHasAuraOrSpell("player", "Instant Poison", "HELPFUL")),
    }
}

Triggers.Subtlety.KidneyShot = {
    Name="Kidney Shot",
    Binding=scripty.Bindings.KidneyShot,
    Conditions={
        sc.AbilityUsable("Kidney Shot"),
        sc.StunnableUnitCasting("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Kick = {
    Name="Kick",
    Binding=scripty.Bindings.Kick,
    Conditions={
        sc.AbilityUsable("Kick"),
        sc.TargetIsReadyForInterrupt(),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Gouge = {
    Name="Gouge",
    Binding=scripty.Bindings.Gouge,
    Conditions={
        sc.AbilityUsable("Gouge"),
        sc.StunnableUnitCasting("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Feint = {
    Name="Feint",
    Binding=scripty.Bindings.Feint,
    Conditions={
        sc.AbilityUsable("Feint"),
        sc.UnitHPDamageAtLeastTakenSince("player", 10, 1),
        Inverse(sc.AbilityWasCastRecently("Feint", 8)),
    }
}

Triggers.Subtlety.Eviscerate = {
    Name="Eviscerate",
    Binding=scripty.Bindings.Eviscerate,
    Conditions={
        sc.AbilityUsable("Eviscerate"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_COMBOPOINTS, 5),
    }

}

Triggers.Subtlety.Evasion = {
    Name="Evasion",
    Binding=scripty.Bindings.Evasion,
    Conditions={
        sc.AbilityUsable("Evasion"),
        sc.UnitHPLessThan("player", 50),
    }
}

Triggers.Subtlety.EchoingReprimand = {
    Name="Echoing Reprimand",
    Binding=scripty.Bindings.EchoingReprimand,
    Conditions={
        sc.AbilityUsable("Echoing Reprimand"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Subtlety.CrimsonVial = {
    Name="Crimson Vial",
    Binding=scripty.Bindings.CrimsonVial,
    Conditions={
        sc.AbilityUsable("Crimson Vial"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.Subtlety.ShroudOfConcealment = {
    Name="Shroud of Concealment",
    Binding=scripty.Bindings.ShroudOfConcealment,
    Conditions={
        function() return false end,
    }
}

Triggers.Subtlety.Distract = {
    Name="Distract",
    Binding=scripty.Bindings.Distract,
    Conditions={
        function() return false end,
    }
}

Triggers.Subtlety.CloakOfShadows = {
    Name="Cloak of Shadows",
    Binding=scripty.Bindings.CloakOfShadows,
    Conditions={
        function() return false end,
    }
}

Triggers.Subtlety.Shadowstrike = {
    Name="Shadowstrike",
    Binding=scripty.Bindings.Shadowstrike,
    Conditions={
        sc.AbilityUsable("Shadowstrike"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.CheapShot = {
    Name="Cheap Shot",
    Binding=scripty.Bindings.CheapShot,
    Conditions={
        sc.AbilityUsable("Cheap Shot"),
        sc.UnitIsPlayer("target"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.AbilityWasCastRecently("Cheap Shot", 15)),
        Inverse(sc.UnitHasAura("target", "Cheap Shot", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Kidney Shot", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Hammer of Justice", "HARMFUL")),
    }
}

Triggers.Subtlety.Blind = {
    Name="Blind",
    Binding=scripty.Bindings.Blind,
    Conditions={
        sc.AbilityUsable("Blind"),
        sc.StunnableUnitCasting("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Subtlety.Blind2 = {
    Name="Blind",
    Binding=scripty.Bindings.Blind,
    Conditions={
        sc.UnitNameIs("target", "Orb of Ascendance"),
        sc.AbilityUsable("Blind"),
        sc.AbilityInRange("Blind", "target"),
    }
}

--Boomkin
Triggers.Boomkin.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellBoomie(),
    }
}

Triggers.Boomkin.StopCastingForFrenzy = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.UnitHasAura("player", "Frenzied Regeneration", "HELPFUL"),
        sc.UnitHPLessThan("player", 95),
    }
}

Triggers.Boomkin.BearForm = {
    Name="Bear Form",
    Binding=scripty.Bindings.BearForm,
    Conditions={
        sc.AbilityUsable("Bear Form"),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        sc.SpellChargesGreaterThan("Frenzied Regeneration", 0),
        Inverse(sc.UnitHasAura("player", "Frenzied Regeneration", "HELPFUL")),
        sc.UnitHPLessThan("player", 60),
    }
}

Triggers.Boomkin.FrenziedRegeneration = {
    Name="Frenzied Regeneration",
    Binding=scripty.Bindings.FrenziedRegeneration,
    Conditions={
        sc.AbilityUsable("Frenzied Regeneration"),
        sc.UnitHPLessThan("player", 65),
        Inverse(sc.UnitHasAura("player", "Frenzied Regeneration", "HELPFUL")),
        sc.SpellChargesGreaterThan("Frenzied Regeneration", 0),
        Inverse(sc.UnitHasAura("player", "Frenzied Regeneration", "HELPFUL")),
    }
}

Triggers.Boomkin.Barkskin = {
    Name="Barkskin",
    Binding=scripty.Bindings.Barkskin,
    Conditions={
        sc.AbilityUsable("Barkskin"),
        sc.UnitHPLessThan("player", 80),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.Boomkin.HeartOfTheWild = {
    Name="Heart of the Wild",
    Binding=scripty.Bindings.HeartOfTheWild,
    Conditions={
        sc.AbilityUsable("Heart of the Wild"),
        sc.UnitHPLessThan("player", 60),
    }
}

Triggers.Boomkin.MoonfireExplosives = {
    Name="Moonfire Explosives",
    Binding=scripty.Bindings.Moonfire,
    Conditions={
        sc.AbilityUsable("Moonfire"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
    }
}

Triggers.Boomkin.RegrowthMe = {
    Name="Regrowth",
    Focus="player",
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHPLessThan("player", 60),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
    }
}

Triggers.Boomkin.RegrowthSave = {
    Name="Regrowth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 30),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
    }
}

Triggers.Boomkin.RegrowthLow = {
    Name="Regrowth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 60),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
    }
}

Triggers.Boomkin.MoonkinForm = {
    Name="Moonkin Form",
    Binding=scripty.Bindings.MoonkinForm,
    Conditions={
        sc.AbilityUsable("Moonkin Form"),
        Inverse(sc.UnitHasAura("player", "Moonkin Form", "HELPFUL")),
        sc.PlayerStandingStill(),
    }
}

Triggers.Boomkin.MoonkinForm2 = {
    Name="Moonkin Form",
    Binding=scripty.Bindings.MoonkinForm,
    Conditions={
        sc.AbilityUsable("Moonkin Form"),
        Inverse(sc.UnitHasAura("player", "Moonkin Form", "HELPFUL")),
        sc.UnitAttackable("target"),
    }
}

Triggers.Boomkin.WarriorOfElune2 = {
    Name="Warrior of Elune",
    Binding=scripty.Bindings.WarriorOfElune,
    Conditions={
        sc.AbilityUsable("Warrior of Elune"),
        sc.AbilityUsable("Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.UnitHasAura("player", "Warrior of Elune", "HELPFUL"),
    }
}

Triggers.Boomkin.Starfire = {
    Name="Starfire",
    Binding=scripty.Bindings.Starfirez,
    Conditions={
        sc.AbilityUsable("Starfire"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        -- sc.NumberOfEnemiesGreaterThan(2),
    }
}

Triggers.Boomkin.WrathForMoonkin = {
    Name="Wrath",
    Binding=scripty.Bindings.Wrath,
    Conditions={
        sc.AbilityUsable("Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.UnitHasAura("player", "Moonkin Form", "HELPFUL")),
    }
}

Triggers.Boomkin.WrathForEclipse = {
    Name="Wrath",
    Binding=scripty.Bindings.Wrath,
    Conditions={
        sc.AbilityUsable("Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.UnitHasAura("player", "Eclipse (Lunar)", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Eclipse (Solar)", "HELPFUL")),
        -- sc.NumberOfEnemiesGreaterThan(2),
    }
}

Triggers.Boomkin.WarriorOfElune = {
    Name="Warrior of Elune",
    Binding=scripty.Bindings.WarriorOfElune,
    Conditions={
        sc.AbilityUsable("Warrior of Elune"),
        sc.AbilityUsable("Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.UnitHasAura("player", "Eclipse (Lunar)", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Eclipse (Solar)", "HELPFUL")),
    }
}

Triggers.Boomkin.StarfireForEclipse = {
    Name="Starfire",
    Binding=scripty.Bindings.Starfire,
    Conditions={
        sc.AbilityUsable("Starfire"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.UnitHasAura("player", "Eclipse (Lunar)", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Eclipse (Solar)", "HELPFUL")),
    }
}

Triggers.Boomkin.StarsurgeAvoidCap = {
    Name="Starsurge",
    Binding=scripty.Bindings.Starsurge,
    Conditions={
        sc.AbilityUsable("Starsurge"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_LUNARPOWER, 80),
    }
}

Triggers.Boomkin.StarfallAvoidCap = {
    Name="Starfall",
    Binding=scripty.Bindings.Starfall,
    Conditions={
        sc.AbilityUsable("Starfall"),
        sc.NumberOfEnemiesGreaterThan(2),
        sc.PlayerResourceMinimum(SOPHIE_POWER_LUNARPOWER, 80),
    }
}

Triggers.Boomkin.StarfallProc = {
    Name="Starfall",
    Binding=scripty.Bindings.Starfall,
    Conditions={
        sc.AbilityUsable("Starfall"),
        sc.UnitHasAura("player", "Starweaver's Warp", "HELPFUL"),
    }
}

Triggers.Boomkin.StarsurgeProc = {
    Name="Starsurge",
    Binding=scripty.Bindings.Starsurge,
    Conditions={
        sc.AbilityUsable("Starsurge"),
        sc.UnitHasAura("player", "Starweaver's Weft", "HELPFUL"),
    }
}

Triggers.Boomkin.Starsurge = {
    Name="Starsurge",
    Binding=scripty.Bindings.Starsurge,
    Conditions={
        sc.AbilityUsable("Starsurge"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_LUNARPOWER, 50),
    }
}

Triggers.Boomkin.ConvokeTheSpirits = {
    Name="Convoke the Spirits",
    Binding=scripty.Bindings.ConvokeTheSpirits,
    Conditions={
        sc.AbilityUsable("Convoke the Spirits"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.IsUnitInCombat("target"),
        sc.IsUnitInCombat("player"),
        sc.UnitHasAura("player", "Eclipse (Lunar)", "HELPFUL")
    }
}

Triggers.Boomkin.ConvokeTheSpirits2 = {
    Name="Convoke the Spirits",
    Binding=scripty.Bindings.ConvokeTheSpirits,
    Conditions={
        sc.AbilityUsable("Convoke the Spirits"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.IsUnitInCombat("target"),
        sc.IsUnitInCombat("player"),
        sc.UnitHasAura("player", "Eclipse (Solar)", "HELPFUL")
    }
}

Triggers.Boomkin.Typhoon = {
    Name="Typhoon",
    Binding=scripty.Bindings.Typhoon,
    Conditions={
        function() return false end,
    }
}

Triggers.Boomkin.EntanglingRoots = {
    Name="Entangling Roots",
    Binding=scripty.Bindings.EntanglingRoots,
    Conditions={
        function() return false end,
    }
}

Triggers.Boomkin.UrsolsVortex = {
    Name="Ursol's Vortex",
    Binding=scripty.Bindings.UrsolsVortex,
    Conditions={
        function() return false end,
    }
}

Triggers.Boomkin.AstralCommunion = {
    Name="Astral Communion",
    Binding=scripty.Bindings.AstralCommunion,
    Conditions={
        sc.AbilityUsable("Astral Communion"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_LUNARPOWER, 40)),
    }
}

Triggers.Boomkin.CelestialAlignment = {
    Name="Celestial Alignment",
    Binding=scripty.Bindings.CelestialAlignment,
    Conditions={
        sc.AbilityUsable("Celestial Alignment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.UnitHasAura("player", "Celestial Alignment", "HELPFUL")),

    }
}

Triggers.Boomkin.ConvokeTheSpirits = {
    Name="Convoke the Spirits",
    Binding=scripty.Bindings.ConvokeTheSpirits,
    Conditions={
        sc.AbilityUsable("Convoke the Spirits"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
    }
}

Triggers.Boomkin.FuryOfElune = {
    Name="Fury of Elune",
    Binding=scripty.Bindings.FuryOfElune,
    Conditions={
        sc.AbilityUsable("Fury of Elune"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
    }
}

Triggers.Boomkin.Starfall = {
    Name="Starfall",
    Binding=scripty.Bindings.Starfall,
    Conditions={
        sc.AbilityUsable("Starfall"),
        sc.NumberOfEnemiesGreaterThan(2),
    }
}

Triggers.Boomkin.StellarFlare = {
    Name="Stellar Flare",
    Binding=scripty.Bindings.StellarFlare,
    Conditions={
        sc.AbilityUsable("Stellar Flare"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        Inverse(sc.UnitHasAura("target", "Stellar Flare", "PLAYER | HARMFUL")),
    }
}

Triggers.Boomkin.Moonfire = {
    Name="Moonfire",
    Binding=scripty.Bindings.Moonfire,
    Conditions={
        sc.AbilityUsable("Moonfire"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Moonfire", "PLAYER | HARMFUL")),
    }
}

Triggers.Boomkin.Sunfire = {
    Name="Sunfire",
    Binding=scripty.Bindings.Sunfire,
    Conditions={
        sc.AbilityUsable("Sunfire"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Sunfire", "PLAYER | HARMFUL")),
    }
}

Triggers.Boomkin.WildMushroom = {
    Name="Wild Mushroom",
    Binding=scripty.Bindings.WildMushroom,
    Conditions={
        sc.AbilityUsable("Wild Mushroom"),
        sc.UnitAttackable("target"),
        Inverse(sc.AbilityWasCastRecently("Wild Mushroom", 8)),
    }
}

Triggers.Boomkin.WildMushroomAOE = {
    Name="Wild Mushroom",
    Binding=scripty.Bindings.WildMushroom,
    Conditions={
        sc.AbilityUsable("Wild Mushroom"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(2),
        Inverse(sc.AbilityWasCastRecently("Wild Mushroom", 8)),
    }
}

Triggers.Boomkin.SolarBeam = {
    Name="Solar Beam",
    Binding=scripty.Bindings.SolarBeam,
    Conditions={
        sc.AbilityUsable("Solar Beam"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.UnitCastingInterruptible("target"),
    }
}

Triggers.Boomkin.MightyBash = {
    Name="Mighty Bash",
    Binding=scripty.Bindings.MightyBash,
    Conditions={
        sc.AbilityUsable("Mighty Bash"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Mighty Bash", "target"),
        sc.StunnableUnitCasting("target"),
    }
}

--Tree
Triggers.Tree.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellTree(),
    }
}

Triggers.Tree.Rebirth = {
    Name="Rebirth",
    Focus={name="deadGuyTarget"},
    Binding=scripty.Bindings.Rebirth,
    Conditions={
        sc.AbilityUsable("Rebirth"),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.Tree.CureAfflicted = {
    Name="Afflicted Cleanse",
    Binding=scripty.Bindings.mNature,
    Conditions={
        sc.AbilityUsable("Nature's Cure"),
        sc.UnitNameIs("mouseover", "Afflicted Soul"),
    }
}

Triggers.Tree.SleepGhosts = {
    Name="Hibernate",
    Binding=scripty.Bindings.Hibernate,
    Conditions={
        sc.AbilityUsable("Hibernate"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
    }
}

Triggers.Tree.ConvokeTheSpirits = {
    Name="Convoke the Spirits",
    Binding=scripty.Bindings.ConvokeTheSpirits,
    Conditions={
        sc.AbilityUsable("Convoke the Spirits"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
    }
}

Triggers.Tree.Barkskin = {
    Name="Barkskin",
    Binding=scripty.Bindings.Barkskin,
    Conditions={
        sc.AbilityUsable("Barkskin"),
        sc.UnitHPDamageAtLeastTakenSince("player", 20, 6),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
    }
}

Triggers.Tree.Starfire = {
    Name="Starfire",
    Binding=scripty.Bindings.Starfire,
    Conditions={
        sc.AbilityUsable("Starfire"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.NumberOfEnemiesGreaterThan(2),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 70),
    }
}

Triggers.Tree.Wrath = {
    Name="Wrath",
    Binding=scripty.Bindings.Wrath,
    Conditions={
        sc.AbilityUsable("Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
    }
}

Triggers.Tree.Sunfire = {
    Name="Sunfire",
    Binding=scripty.Bindings.Sunfire,
    Conditions={
        sc.AbilityUsable("Sunfire"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Sunfire", "PLAYER | HARMFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 70),
        sc.AbilityInRange("Wrath", "target"),
    }
}

Triggers.Tree.Starsurge = {
    Name="Starsurge",
    Binding=scripty.Bindings.Starsurge,
    Conditions={
        sc.AbilityUsable("Starsurge"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 70),
    }
}

Triggers.Tree.Invigorate = {
    Name="Invigorate",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Invigorate,
    Conditions={
        sc.AbilityUsable("Invigorate"),
        sc.UnitHasAura({name="bestHealTarget"}, "Rejuvenation", "PLAYER | HELPFUL"),
        sc.UnitAuraDurationLessThan({name="bestHealTarget"}, "Rejuvenation", 10, "PLAYER | HELPFUL"),
    }
}

Triggers.Tree.CenarionWard = {
    Name="Cenarion Ward",
    Focus={name="bestPrepTarget"},
    Binding=scripty.Bindings.CenarionWard,
    Conditions={
        sc.AbilityUsable("Cenarion Ward"),
    }
}

Triggers.Tree.CenarionWardTankDying = {
    Name="Cenarion Ward",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.CenarionWard,
    Conditions={
        sc.AbilityUsable("Cenarion Ward"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.UnitRoleIs({name="bestHealTarget"}, "TANK"),
    }
}

Triggers.Tree.Swiftmend = {
    Name="Swiftmend",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Swiftmend,
    Conditions={
        sc.AbilityUsable("Swiftmend"),
        sc.UnitHasAura({name="bestHealTarget"}, "Rejuvenation", "PLAYER | HELPFUL"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Tree.Swiftmend2 = {
    Name="Swiftmend",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Swiftmend,
    Conditions={
        sc.AbilityUsable("Swiftmend"),
        sc.UnitHasAura({name="bestHealTarget"}, "Regrowth", "PLAYER | HELPFUL"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Tree.Swiftmend3 = {
    Name="Swiftmend",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Swiftmend,
    Conditions={
        sc.AbilityUsable("Swiftmend"),
        sc.UnitHasAura({name="bestHealTarget"}, "Wild Growth", "PLAYER | HELPFUL"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}


Triggers.Tree.GroveGuardians = {
    Name="Grove Guardians",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.GroveGuardians,
    Conditions={
        sc.AbilityUsable("Grove Guardians"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Tree.Incarnation = {
    Name="Incarnation: Tree of Life",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Incarnation,
    Conditions={
        sc.AbilityUsable("Incarnation: Tree of Life"),
        Inverse(sc.UnitHasAura("player", "Incarnation: Tree of Life", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Tree.Nourish = {
    Name="Nourish",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Nourish,
    Conditions={
        sc.AbilityUsable("Nourish"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.UnitAuraCountLessThan("player", "Wild Synthesis", 3, "PLAYER | HELPFUL")),
    }
}

Triggers.Tree.Regrowth = {
    Name="Regrowth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Regrowthz,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
    }
}

Triggers.Tree.AdaptiveSwarmHurt = {
    Name="Adaptive Swarm",
    Binding=scripty.Bindings.AdaptiveSwarm,
    Conditions={
        sc.AbilityUsable("Adaptive Swarm"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
    }
}

Triggers.Tree.NaturesVigil = {
    Name="Nature's Vigil",
    Binding=scripty.Bindings.NaturesVigil,
    Conditions={
        sc.AbilityUsable("Nature's Vigil"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrath", "target"),
    }
}

Triggers.Tree.Renewal = {
    Name="Renewal",
    Binding=scripty.Bindings.Renewal,
    Conditions={
        sc.AbilityUsable("Renewal"),
        sc.UnitHPLessThan("player", 60),
    }
}

Triggers.Tree.Innervate = {
    Name="Innervate",
    Focus="player",
    Binding=scripty.Bindings.Innervate,
    Conditions={
        sc.AbilityUsable("Innervate"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80)),
    }
}

Triggers.Tree.AdaptiveSwarmHeal = {
    Name="Adaptive Swarm",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.AdaptiveSwarmHeal,
    Conditions={
        sc.AbilityUsable("Adaptive Swarm"),
    }
}

Triggers.Tree.RegrowthProc = {
    Name="Regrowth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHasAura("player", "Clearcasting", "PLAYER | HELPFUL"),
        Inverse(sc.UnitHasAura("player", "Innervate", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Tree.Lifebloom = {
    Name="Lifebloom",
    Focus={name="bestBloomTarget"},
    Binding=scripty.Bindings.Lifebloom,
    Conditions={
        sc.AbilityUsable("Lifebloom"),
        Inverse(sc.LifebloomActive()),
    }
}

Triggers.Tree.LifebloomMe = {
    Name="Lifebloom",
    Focus="player",
    Binding=scripty.Bindings.Lifebloom,
    Conditions={
        sc.AbilityUsable("Lifebloom"),
        Inverse(sc.UnitHasAura("player", "Lifebloom", "PLAYER | HELPFUL")),
    }
}

Triggers.Tree.LifebloomLow = {
    Name="Lifebloom",
    Focus="player",
    Binding=scripty.Bindings.Lifebloom,
    Conditions={
        sc.AbilityUsable("Lifebloom"),
        sc.UnitAuraDurationLessThan("player", "Lifebloom", 4, "PLAYER | HELPFUL"),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_CAT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Cat Form", "HELPFUL")),
    }
}

Triggers.Tree.Efflorescence = {
    Name="Efflorescence",
    Binding=scripty.Bindings.Efflorescence,
    Conditions={
        sc.AbilityUsable("Efflorescence"),
        -- sc.PlayerStandingStill(),
        Inverse(sc.EffloActive()),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_CAT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Cat Form", "HELPFUL")),
    }
}

Triggers.Tree.Efflorescence2 = {
    Name="Efflorescence",
    Binding=scripty.Bindings.Efflorescence,
    Conditions={
        sc.AbilityUsable("Efflorescence"),
        -- sc.PlayerStandingStill(),
        Inverse(sc.EffloActive()),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_CAT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Cat Form", "HELPFUL")),
    }
}

Triggers.Tree.Rejuvenation = {
    Name="Rejuvenation",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Rejuvenation,
    Conditions={
        sc.AbilityUsable("Rejuvenation"),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Rejuvenation (Germination)", "PLAYER | HELPFUL")),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.Tree.Rejuvenation2 = {
    Name="Rejuvenation",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Rejuvenation,
    Conditions={
        sc.AbilityUsable("Rejuvenation"),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Rejuvenation", "PLAYER | HELPFUL")),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.Tree.RejuvenationBest = {
    Name="Rejuvenation Best",
    Focus={name="bestRejuvTarget"},
    Binding=scripty.Bindings.Rejuvenation,
    Conditions={
        sc.AbilityUsable("Rejuvenation"),
        Inverse(sc.UnitHasAura({name="bestRejuvTarget"}, "Rejuvenation", "PLAYER | HELPFUL")),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
    }
}

Triggers.Tree.RejuvenationProc = {
    Name="Rejuvenation",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Rejuvenation,
    Conditions={
        sc.AbilityUsable("Rejuvenation"),
        sc.UnitHasAura("player", "Power of the Archdruid", "PLAYER | HELPFUL"),
    }
}

Triggers.Tree.WildGrowth = {
    Name="Wild Growth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WildGrowth,
    Conditions={
        sc.AbilityUsable("Wild Growth"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 90),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
    }
}

Triggers.Tree.WildGrowthProc = {
    Name="Wild Growth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WildGrowth,
    Conditions={
        sc.AbilityUsable("Wild Growth"),
        sc.UnitHasAura("player", "Soul of the Forest", "PLAYER | HELPFUL"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
    }
}

Triggers.Tree.NaturesCure = {
    Name="Nature's Cure",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.NaturesCure,
    Conditions={
        sc.AbilityUsable("Nature's Cure"),
        Inverse(sc.PlayerIsInPvPZone()),
    }
}

Triggers.Tree.Ironbark = {
    Name="Ironbark",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Ironbark,
    Conditions={
        sc.AbilityUsable("Ironbark"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
    }
}

Triggers.Tree.Tranquility = {
    Name="Tranquility",
    Binding=scripty.Bindings.Tranquility,
    Conditions={
        sc.AbilityUsable("Tranquility"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 60),
        sc.PlayerStandingStill(),
    }
}

Triggers.Tree.NaturesSwiftnessD = {
    Name="Natures Swiftness",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.NaturesSwiftnessD,
    Conditions={
        sc.AbilityUsable("Nature's Swiftness"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
    }
}

--LaserBear
Triggers.LaserBear.RemoveCorruption = {
    Name="Remove Corruption",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.RemoveCorruption,
    Conditions={
        sc.AbilityUsable("Remove Corruption"),
    }
}

Triggers.LaserBear.Fleshcraft = {
    Name="Fleshcraft",
    Binding=scripty.Bindings.Fleshcraft,
    Conditions={
        sc.AbilityUsable("Fleshcraft"),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.PlayerIsDoingSomething()),
    }
}

Triggers.LaserBear.FleshcraftLow = {
    Name="Fleshcraft",
    Binding=scripty.Bindings.Fleshcraft,
    Conditions={
        sc.AbilityUsable("Fleshcraft"),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.LaserBear.IncapacitatingRoar = {
    Name="Incapacitating Roar",
    Focus="player",
    Binding=scripty.Bindings.IncapacitatingRoar,
    Conditions={
        sc.AbilityUsable("Incapacitating Roar"),
        sc.EnemyInMeleeNeedsStun(),
    }
}

Triggers.LaserBear.RegrowthProc = {
    Name="Regrowth",
    Focus="player",
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHPLessThan("player", 65),
        sc.UnitHasAura("player", "Dream of Cenarius", "HELPFUL"),
    }
}

Triggers.LaserBear.RegrowthProc2 = {
    Name="Regrowth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.UnitHasAura("player", "Dream of Cenarius", "HELPFUL"),
    }
}

Triggers.LaserBear.SurvivalInstincts = {
    Name="Survival Instincts",
    Binding=scripty.Bindings.SurvivalInstincts,
    Conditions={
        sc.AbilityUsable("Survival Instincts"),
        sc.SpellChargesGreaterThan("Survival Instincts", 1),
        sc.UnitHPDamageAtLeastTakenSince("player", 20, 3),
        Inverse(sc.UnitHasAura("player", "Survival Instincts", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rage of the Sleeper", "HELPFUL")),
    }
}

Triggers.LaserBear.SurvivalInstincts2 = {
    Name="Survival Instincts",
    Binding=scripty.Bindings.SurvivalInstincts,
    Conditions={
        sc.AbilityUsable("Survival Instincts"),
        sc.UnitHPDamageAtLeastTakenSince("player", 40, 3),
        Inverse(sc.UnitHasAura("player", "Survival Instincts", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rage of the Sleeper", "HELPFUL")),
    }
}

Triggers.LaserBear.MaulProc = {
    Name="Maul",
    Binding=scripty.Bindings.Maul,
    Conditions={
        sc.AbilityUsable("Maul"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Tooth and Claw", "HELPFUL"),
    }
}

Triggers.LaserBear.Maul = {
    Name="Maul",
    Binding=scripty.Bindings.Maul,
    Conditions={
        sc.AbilityUsable("Maul"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RAGE, 60),
    }
}

Triggers.LaserBear.ConvokeTheSpirits = {
    Name="Convoke the Spirits",
    Binding=scripty.Bindings.ConvokeTheSpirits,
    Conditions={
        sc.AbilityUsable("Convoke the Spirits"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
        Inverse(sc.UnitHasAura("player", "Survival Instincts", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rage of the Sleeper", "HELPFUL")),
    }
}

Triggers.LaserBear.LunarBeam = {
    Name="Lunar Beam",
    Binding=scripty.Bindings.LunarBeam,
    Conditions={
        sc.AbilityUsable("Lunar Beam"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.LaserBear.ThrashAOE = {
    Name="Thrash",
    Binding=scripty.Bindings.Thrash,
    Conditions={
        sc.AbilityUsable("Thrash"),
        sc.NumberOfMeleeEnemiesGreaterThan(3),
        Inverse(sc.AbilityWasCastRecently("Thrash", 6)),
    }
}

Triggers.LaserBear.Thrash = {
    Name="Thrash",
    Binding=scripty.Bindings.Thrash,
    Conditions={
        sc.AbilityUsable("Thrash"),
        sc.UnitAttackable("target"),
        sc.UnitAuraCountLessThan("target", "Thrash", 3, "PLAYER | HARMFUL"),
        Inverse(sc.AbilityWasCastRecently("Thrash", 6)),
    }
}

Triggers.LaserBear.ThrashSpam = {
    Name="Thrash",
    Binding=scripty.Bindings.Thrash,
    Conditions={
        sc.AbilityUsable("Thrash"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
        Inverse(sc.AbilityWasCastRecently("Thrash", 6)),
    }
}

Triggers.LaserBear.SwipeSpam = {
    Name="Swipe",
    Binding=scripty.Bindings.Swipe,
    Conditions={
        sc.AbilityUsable("Swipe"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
    }
}

Triggers.LaserBear.Swipe = {
    Name="Swipe",
    Binding=scripty.Bindings.Swipe,
    Conditions={
        sc.AbilityUsable("Swipe"),
        sc.NumberOfMeleeEnemiesGreaterThan(1),
    }
}

Triggers.LaserBear.SwipeAOE = {
    Name="Swipe",
    Binding=scripty.Bindings.Swipe,
    Conditions={
        sc.AbilityUsable("Swipe"),
        sc.NumberOfMeleeEnemiesGreaterThan(3),
    }
}

Triggers.LaserBear.CatForm = {
    Name="Cat Form",
    Binding=scripty.Bindings.CatForm,
    Conditions={
        sc.AbilityUsable("Cat Form"),
        sc.PlayerMoving(),
        sc.PlayerBeenMoving(),
        Inverse(sc.UnitUnderAttack("player")),
        Inverse(sc.UnitHPDamageAtLeastTakenSince("player", 1, 3)),
        Inverse(sc.NumberOfEnemiesGreaterThan(0)),
        Inverse(sc.UnitHasAura("player", "Stampeding Roar", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_CAT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Cat Form", "HELPFUL")),
        Inverse(sc.IsUnitInCombat("player")),
    }
}

Triggers.LaserBear.TravelForm = {
    Name="Travel Form",
    Binding=scripty.Bindings.TravelForm,
    Conditions={
        sc.AbilityUsable("Travel Form"),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
        Inverse(sc.IsUnitInCombat("player")),
        Inverse(sc.PotentialLoot()),
        Inverse(sc.PlayerIsIndoors()),
    }
}

Triggers.LaserBear.TigerDash = {
    Name="Tiger Dash",
    Binding=scripty.Bindings.TigerDash,
    Conditions={
        sc.AbilityUsable("Tiger Dash"),
        sc.UnitSpeedLessThan("player", 11),
        sc.PlayerMoving(),
        sc.PlayerBeenMoving(),
        sc.AbilityOnCD("Stampeding Roar"),
        Inverse(sc.UnitHasAura("player", "Stampeding Roar", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.StampedingRoar = {
    Name="Stampeding Roar",
    Binding=scripty.Bindings.StampedingRoar,
    Conditions={
        sc.AbilityUsable("Stampeding Roar"),
        sc.UnitSpeedLessThan("player", 11),
        sc.PlayerMoving(),
        sc.PlayerBeenMoving(),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.Soothe = {
    Name="Soothe",
    Binding=scripty.Bindings.Soothe,
    Conditions={
        sc.AbilityUsable("Soothe"),
        sc.EnemyTargetNeedsSooth(),
    }
}

Triggers.LaserBear.SkullBash = {
    Name="Skull Bash",
    Binding=scripty.Bindings.SkullBash,
    Conditions={
        sc.AbilityUsable("Skull Bash"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.LaserBear.Shred = {
    Name="Shred",
    Binding=scripty.Bindings.Shred,
    Conditions={
        sc.AbilityUsable("Shred"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.LaserBear.Renewal = {
    Name="Renewal",
    Focus="player",
    Binding=scripty.Bindings.Renewal,
    Conditions={
        sc.AbilityUsable("Renewal"),
        sc.UnitHPLessThan("player", 35),
    }
}

Triggers.LaserBear.NaturesVigil = {
    Name="Nature's Vigil",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.NaturesVigil,
    Conditions={
        sc.AbilityUsable("Nature's Vigil"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.UnitAttackable("target"),
    }
}

Triggers.LaserBear.Regrowth = {
    Name="Regrowth",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Regrowth,
    Conditions={
        sc.AbilityUsable("Regrowth"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Cat Form", "HELPFUL")),
    }
}

Triggers.LaserBear.Prowl = {
    Name="Prowl",
    Binding=scripty.Bindings.Prowl,
    Conditions={
        sc.AbilityUsable("Prowl"),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.Moonfire = {
    Name="Moonfire",
    Binding=scripty.Bindings.Moonfire,
    Conditions={
        sc.AbilityUsable("Moonfire"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Moonfire", "PLAYER | HARMFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 70),
    }
}

Triggers.LaserBear.MoonfireProc = {
    Name="Moonfire",
    Binding=scripty.Bindings.Moonfire,
    Conditions={
        sc.AbilityUsable("Moonfire"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("player", "Galactic Guardian", 2, "HELPFUL"),
        Inverse(sc.UnitAuraDurationLessThan("player", "Galactic Guardian", 0, "HELPFUL")),
    }
}

Triggers.LaserBear.MoonfireSpam = {
    Name="Moonfire",
    Binding=scripty.Bindings.Moonfire,
    Conditions={
        sc.AbilityUsable("Moonfire"),
        sc.UnitAttackable("target"),
    }
}


Triggers.LaserBear.MarkOfTheWild = {
    Name="Mark of the Wild",
    Binding=scripty.Bindings.MarkOfTheWild,
    Conditions={
        sc.AbilityUsable("Mark of the Wild"),
        sc.MarkNeeded(),
    }
}

Triggers.LaserBear.MangleProc = {
    Name="Mangle",
    Binding=scripty.Bindings.Mangle,
    Conditions={
        sc.AbilityUsable("Mangle"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Gore", "PLAYER | HELPFUL"),
    }
}

Triggers.LaserBear.Mangle = {
    Name="Mangle",
    Binding=scripty.Bindings.Mangle,
    Conditions={
        sc.AbilityUsable("Mangle"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.LaserBear.Ironfur = {
    Name="Ironfur",
    Binding=scripty.Bindings.Ironfur,
    Conditions={
        sc.AbilityUsable("Ironfur"),
        sc.UnitAuraCountLessThan("player", "Ironfur", 1, "HELPFUL"),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.LaserBear.Ironfur2 = {
    Name="Ironfur",
    Binding=scripty.Bindings.Ironfur,
    Conditions={
        sc.AbilityUsable("Ironfur"),
        sc.UnitHPDamagePhysicalAtLeastTakenSince("player", 30, 2),
    }
}

Triggers.LaserBear.Ironfur3 = {
    Name="Ironfur",
    Binding=scripty.Bindings.Ironfur,
    Conditions={
        sc.AbilityUsable("Ironfur"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RAGE, 80),
    }
}

Triggers.LaserBear.HeartOfTheWild = {
    Name="Heart of the Wild",
    Binding=scripty.Bindings.HeartOfTheWild,
    Conditions={
        sc.AbilityUsable("Heart of the Wild"),
        sc.UnitAttackable("target"),
    }
}

Triggers.LaserBear.Growl = {
    Name="Growl",
    Binding=scripty.Bindings.Growl,
    Conditions={
        sc.AbilityUsable("Growl"),
        sc.ProtPalaUnitNeedsTaunt("target"),
    }
}

Triggers.LaserBear.FrenziedRegeneration = {
    Name="Frenzied Regeneration",
    Binding=scripty.Bindings.FrenziedRegeneration,
    Conditions={
        sc.AbilityUsable("Frenzied Regeneration"),
        sc.SpellChargesGreaterThan("Frenzied Regeneration", 1),
        sc.UnitHPLessThan("player", 65),
        Inverse(sc.UnitHasAura("player", "Frenzied Regeneration", "HELPFUL")),
    }
}


Triggers.LaserBear.FrenziedRegeneration2 = {
    Name="Frenzied Regeneration",
    Binding=scripty.Bindings.FrenziedRegeneration,
    Conditions={
        sc.AbilityUsable("Frenzied Regeneration"),
        sc.UnitHPLessThan("player", 50),
        Inverse(sc.UnitHasAura("player", "Frenzied Regeneration", "HELPFUL")),
    }
}

Triggers.LaserBear.BearForm = {
    Name="Bear Form",
    Binding=scripty.Bindings.BearForm,
    Conditions={
        sc.AbilityUsable("Bear Form"),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_MOUNT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_CAT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Cat Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.BearForm4 = {
    Name="Bear Form",
    Binding=scripty.Bindings.BearForm,
    Conditions={
        sc.AbilityUsable("Bear Form"),
        sc.PlayerStandingStill(),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_MOUNT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.BearForm2 = {
    Name="Bear Form",
    Binding=scripty.Bindings.BearForm,
    Conditions={
        sc.AbilityUsable("Bear Form"),
        sc.NumberOfEnemiesGreaterThan(0),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_MOUNT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.BearForm3 = {
    Name="Bear Form",
    Binding=scripty.Bindings.BearForm,
    Conditions={
        sc.AbilityUsable("Bear Form"),
        sc.UnitUnderAttack("player"),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_MOUNT)),
        Inverse(sc.ShapeshiftFormIs(SOPHIE_SHAPESHIFTFORM_TRAVEL)),
        Inverse(sc.UnitHasAura("player", "Bear Form", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Travel Form", "HELPFUL")),
    }
}

Triggers.LaserBear.RageOfTheSleeper = {
    Name="Rage of the Sleeper",
    Binding=scripty.Bindings.RageOfTheSleeper,
    Conditions={
        sc.AbilityUsable("Rage of the Sleeper"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Survival Instincts", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rage of the Sleeper", "HELPFUL")),
    }
}

Triggers.LaserBear.Berserk = {
    Name="Berserk",
    Binding=scripty.Bindings.Berserk,
    Conditions={
        sc.AbilityUsable("Berserk"),
        sc.UnitHPDamageAtLeastTakenSince("player", 20, 6),
        sc.UnitHPLessThan("player", 65),
        Inverse(sc.UnitHasAura("player", "Survival Instincts", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rage of the Sleeper", "HELPFUL")),
    }
}

Triggers.LaserBear.Barkskin = {
    Name="Barkskin",
    Binding=scripty.Bindings.Barkskin,
    Conditions={
        sc.AbilityUsable("Barkskin"),
        sc.UnitHPDamageAtLeastTakenSince("player", 20, 6),
        Inverse(sc.UnitHasAura("player", "Survival Instincts", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Barkskin", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Berserk", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rage of the Sleeper", "HELPFUL")),
    }
}

--Ret
Triggers.Ret.DivineShield = {
    Name="Divine Shield",
    Binding=scripty.Bindings.DivineShield,
    Conditions={
        sc.AbilityUsable("Divine Shield"),
        sc.UnitHPLessThan("player", 30),
        -- Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3)),
        Inverse(sc.UnitHasAura("player", "Forbearance", "HARMFUL")),
    }
}

Triggers.Ret.DivineToll = {
    Name="Divine Toll",
    Binding=scripty.Bindings.DivineTollAttack,
    Conditions={
        sc.AbilityUsable("Divine Toll"),
        sc.AbilityInRange("Blade Of Justice", "target"),
        sc.UnitAttackable("target"),
    }
}
Triggers.Ret.HammerOfWrath = {
    Name="Hammer of Wrath",
    Binding=scripty.Bindings.HammerOfWrath,
    Conditions={
        sc.AbilityUsable("Hammer of Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hammer of Wrath", "target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}

Triggers.Ret.CrusaderExplosives = {
    Name="Crusader Explosives",
    Binding=scripty.Bindings.CrusaderStrike,
    Conditions={
        sc.AbilityUsable("Crusader Strike"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Ret.JudgmentExplosives = {
    Name="Explosives Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
        sc.AbilityInRange("Judgment", "target"),
    }
}

Triggers.Ret.HammerOfWrathExplosives = {
    Name="HammerOfWrath",
    Binding=scripty.Bindings.HammerOfWrath,
    Conditions={
        sc.AbilityUsable("Hammer of Wrath"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
        sc.AbilityInRange("Hammer of Wrath", "target"),
    }
}

Triggers.Ret.AvengersShieldExplosives = {
    Name="Avenger's Shield explosives",
    Binding=scripty.Bindings.AvengersShield,
    Conditions={
        sc.AbilityUsable("Avenger's Shield"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
        sc.AbilityInRange("Hammer of Wrath", "target"),
    }
}

Triggers.Ret.CrusaderExplosives = {
    Name="Crusader Explosives",
    Binding=scripty.Bindings.CrusaderStrike,
    Conditions={
        sc.AbilityUsable("Crusader Strike"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Ret.BladeOfJustice = {
    Name="Blade of Justice",
    Binding=scripty.Bindings.BladeOfJustice,
    Conditions={
        sc.AbilityUsable("Blade of Justice"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Blade of Justice", "target"),
    }
}

Triggers.Ret.DivineStorm = {
    Name="Divine Storm",
    Binding=scripty.Bindings.DivineStorm,
    Conditions={
        sc.AbilityUsable("Divine Storm"),
        sc.NumberOf10YardEnemiesGreaterThan(2),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 4),
    }
} 

Triggers.Ret.ExecutionSentence = {
    Name="Execution Sentence",
    Binding=scripty.Bindings.ExecutionSentence,
    Conditions={
        sc.AbilityUsable("Execution Sentence"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Execution Sentence", "target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Ret.Exorcism = {
    Name="Exorcism",
    Binding=scripty.Bindings.Exorcism,
    Conditions={
        sc.AbilityUsable("Exorcism"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Exorcism", "target"),
    }
}

Triggers.Ret.FinalVerdict = {
    Name="Final Verdict",
    Binding=scripty.Bindings.FinalVerdict,
    Conditions={
        sc.AbilityUsable("Final Verdict"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Templar's Verdict", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 4),
    }
}

Triggers.Ret.WakeOfAshes = {
    Name="Wake of Ashes",
    Binding=scripty.Bindings.WakeOfAshes,
    Conditions={
        sc.AbilityUsable("Wake of Ashes"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Ret.ShieldOfVengeance = {
    Name="Shield of Vengeance",
    Binding=scripty.Bindings.ShieldOfVengeance,
    Conditions={
        sc.AbilityUsable("Shield of Vengeance"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.Ret.WordOfGloryMyself = {
    Name="Word of Glory",
    Focus="player",
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan("player", 50),
    }
}

Triggers.Ret.WordOfGlory = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
    }
}

Triggers.Ret.WordOfGlorySave = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 30),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
    }
}

Triggers.Ret.FlashOfLight = {
    Name="Flash of Light",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.FlashOfLight,
    Conditions={
        sc.AbilityUsable("Flash of Light"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.Ret.FlashOfLightSave = {
    Name="Flash of Light",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.FlashOfLight,
    Conditions={
        sc.AbilityUsable("Flash of Light"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 45),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
    }
}

Triggers.Ret.FlashOfLightMyself = {
    Name="Flash of Light",
    Focus="player",
    Binding=scripty.Bindings.FlashOfLight,
    Conditions={
        sc.AbilityUsable("Flash of Light"),
        sc.UnitHPLessThan("player", 70),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
    }
}

Triggers.Ret.CrusaderStrike = {
    Name="Crusader Strike",
    Binding=scripty.Bindings.CrusaderStrike,
    Conditions={
        sc.AbilityUsable("Crusader Strike"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Crusader Strike", "target"),
    }
}

Triggers.Ret.TemplarSlash = {
    Name="Templar Slash",
    Binding=scripty.Bindings.TemplarSlash,
    Conditions={
        sc.AbilityUsable("Templar Slash"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Templar Slash", "target"),
    }
}

Triggers.Ret.AvengingWrath = {
    Name="Avenging Wrath",
    Binding=scripty.Bindings.AvengingWrath,
    Conditions={
        sc.AbilityUsable("Avenging Wrath"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Ret.RepentanceGhosts = {
    Name="Repentance",
    Binding=scripty.Bindings.Repentance,
    Conditions={
        sc.AbilityUsable("Repentance"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
    }
}

--HolyPala
Triggers.HolyPala.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellPaladin(),
    }
}

Triggers.HolyPala.AvengingWrath = {
    Name="Avenging Wrath",
    Binding=scripty.Bindings.AvengingWrath,
    Conditions={
        sc.AbilityUsable("Avenging Wrath"),
        Inverse(sc.UnitHasAura("player", "Avenging Wrath", "HELPFUL")),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 65),
    }
}

Triggers.HolyPala.LayOnHands = {
    Name="Lay on Hands",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LayOnHands,
    Conditions={
        sc.AbilityUsable("Lay on Hands"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 35),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Forbearance", "HARMFUL")),
        sc.UnitIsNotPaladin({name="bestHealTarget"}),
    }
}

Triggers.HolyPala.Blessing = {
    Name="Blessing",
    Focus="player",
    Binding=scripty.Bindings.Blessing,
    Conditions={
        sc.AbilityUsable("Blessing of Summer"),
    }
}

Triggers.HolyPala.Daybreak = {
    Name="Daybreak",
    Binding=scripty.Bindings.Daybreak,
    Conditions={
        sc.AbilityUsable("Daybreak"),
        sc.AbilityWasCastRecently("Divine Toll", 6),
    }
}

Triggers.HolyPala.JudgmentEmpyrean = {
    Name="Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Judgment", "target"),
        Inverse(sc.UnitHasAura("player", "Empyrean Legacy", "HARMFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3),
    }
}

Triggers.HolyPala.Judgment = {
    Name="Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Judgment", "target"),
    }
}

Triggers.HolyPala.Judgment2 = {
    Name="Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Judgment", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 90),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}

Triggers.HolyPala.JudgmentInfusion = {
    Name="Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Judgment", "target"),
        sc.UnitHasAura("player", "Infusion of Light", "HELPFUL"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}


Triggers.HolyPala.CrusaderStrike = {
    Name="Crusader Strike",
    Binding=scripty.Bindings.CrusaderStrike,
    Conditions={
        sc.AbilityUsable("Crusader Strike"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Crusader Strike", "target"),
    }
}

Triggers.HolyPala.CrusaderStrike2 = {
    Name="Crusader Strike",
    Binding=scripty.Bindings.CrusaderStrike,
    Conditions={
        sc.AbilityUsable("Crusader Strike"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Crusader Strike", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}

Triggers.HolyPala.WordOfGloryAvoidCap = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5),
    }
}

Triggers.HolyPala.WordOfGloryEmpyrean = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
        sc.UnitHasAura("player", "Empyrean Legacy", "HELPFUL"),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.AuraMastery = {
    Name="Aura Mastery",
    Binding=scripty.Bindings.AuraMastery,
    Conditions={
        function() return false end,
    }
}

Triggers.HolyPala.WordOfGloryMyself = {
    Name="Word of Glory",
    Focus="player",
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan("player", 70),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.WordOfGlory2 = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 10),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
    }
}

Triggers.HolyPala.WordOfGlory = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.WordOfGlorySave = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.LightOfTheMartyrMaintain = {
    Name="Light of the Martyr",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LightOfTheMartyr,
    Conditions={
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
        sc.AbilityUsable("Light of the Martyr"),
        Inverse(sc.UnitIsUnit({name="bestHealTarget"}, "player")),
        sc.UnitAuraDurationLessThan("player", "Untempered Dedication", 3, "HELPFUL"),
    }
}

Triggers.HolyPala.LightOfTheMartyr = {
    Name="Light of the Martyr",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LightOfTheMartyr,
    Conditions={
        sc.AbilityUsable("Light of the Martyr"),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        Inverse(sc.UnitIsUnit({name="bestHealTarget"}, "player")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.LightOfTheMartyrSpam = {
    Name="Light of the Martyr",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LightOfTheMartyr,
    Conditions={
        sc.AbilityUsable("Light of the Martyr"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 70),
        Inverse(sc.UnitIsUnit({name="bestHealTarget"}, "player")),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.AvengingCrusader = {
    Name="Avenging Crusader",
    Binding=scripty.Bindings.AvengingCrusader,
    Conditions={
        sc.AbilityUsable("Avenging Crusader"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3),
    }
}

Triggers.HolyPala.AvengingCrusader2 = {
    Name="Avenging Crusader",
    Binding=scripty.Bindings.AvengingCrusader,
    Conditions={
        sc.AbilityUsable("Avenging Crusader"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Divine Purpose", "HELPFUL"),
    }
}

Triggers.HolyPala.Seraphim = {
    Name="Seraphim",
    Binding=scripty.Bindings.Seraphim,
    Conditions={
        sc.AbilityUsable("Seraphim"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5),
    }
}

Triggers.HolyPala.LightOfDawnAvoidCap = {
    Name="Light of Dawn",
    Binding=scripty.Bindings.LightOfDawn,
    Conditions={
        sc.AbilityUsable("Light of Dawn"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.HolyPala.LightOfDawnForMaraad = {
    Name="Light of Dawn",
    Binding=scripty.Bindings.LightOfDawn,
    Conditions={
        sc.AbilityUsable("Light of Dawn"),
        Inverse(sc.UnitHasAura("player", "Maraad's Dying Breath", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 95),
    }
}

Triggers.HolyPala.LightOfDawn = {
    Name="Light of Dawn",
    Binding=scripty.Bindings.LightOfDawn,
    Conditions={
        sc.AbilityUsable("Light of Dawn"),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 90),
    }
}

Triggers.HolyPala.HolyShockAttack = {
    Name="Holy Shock",
    Binding=scripty.Bindings.HolyShockAttack,
    Conditions={
        sc.AbilityUsable("Holy Shock"),
        sc.AbilityInRange("Holy Prism", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 90),
    }
}

Triggers.HolyPala.HolyShockAttack2 = {
    Name="Holy Shock",
    Binding=scripty.Bindings.HolyShockAttack,
    Conditions={
        sc.AbilityUsable("Holy Shock"),
        sc.AbilityInRange("Holy Prism", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 90),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}

Triggers.HolyPala.HolyPrism = {
    Name="Holy Shock",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HolyPrism,
    Conditions={
        sc.AbilityUsable("Holy Prism"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.HolyShock = {
    Name="Holy Shock",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HolyShock,
    Conditions={
        sc.AbilityUsable("Holy Shock"),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="bestHealTarget"}, 95),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 30),

    }
}

Triggers.HolyPala.HolyShock2 = {
    Name="Holy Shock",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HolyShock,
    Conditions={
        sc.AbilityUsable("Holy Shock"),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}

Triggers.HolyPala.HolyLight = {
    Name="Holy Light",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.HolyLight,
    Conditions={
        sc.AbilityUsable("Holy Light"),
        sc.UnitHasAura("player", "Hand of Divinity", "HELPFUL"),
        sc.UnitHasAura("player", "Tyr's Deliverance", "HELPFUL"),
    }
}

Triggers.HolyPala.FlashOfLightInfusion = {
    Name="Flash of Light",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.FlashOfLight,
    Conditions={
        sc.AbilityUsable("Flash of Light"),
        sc.UnitHasAura("player", "Infusion of Light", "HELPFUL"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.HandOfDivinity = {
    Name="Hand of Divinity",
    Binding=scripty.Bindings.HandOfDivinity,
    Conditions={
        sc.AbilityUsable("Hand of Divinity"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.TyrsDeliverance = {
    Name="Tyr's Deliverance",
    Binding=scripty.Bindings.TyrsDeliverance,
    Conditions={
        sc.AbilityUsable("Tyr's Deliverance"),
        sc.UnitHasAura("player", "Hand of Divinity", "HELPFUL"),
    }
}

Triggers.HolyPala.FlashOfLight = {
    Name="Flash of Light",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.FlashOfLight,
    Conditions={
        sc.AbilityUsable("Flash of Light"),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.UnitHPLessThan({name="bestHealTarget"}, 75),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.DivineToll = {
    Name="Divine Toll",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.DivineToll,
    Conditions={
        sc.AbilityUsable("Divine Toll"),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 80),
    }
}

Triggers.HolyPala.DivineToll2 = {
    Name="Divine Toll",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.DivineToll,
    Conditions={
        sc.AbilityUsable("Divine Toll"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 75),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 2)),
    }
}

Triggers.HolyPala.DivineProtection = {
    Name="Divine Protection",
    Binding=scripty.Bindings.DivineProtection,
    Conditions={
        sc.AbilityUsable("Divine Protection"),
        sc.UnitHPDamageAtLeastTakenSince("player", 1, 2),
    }
}

Triggers.HolyPala.BestowFaith = {
    Name="Bestow Faith",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.BestowFaith,
    Conditions={
        sc.AbilityUsable("Bestow Faith"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.HolyPala.HammerOfWrath = {
    Name="Hammer of Wrath",
    Binding=scripty.Bindings.HammerOfWrath,
    Conditions={
        sc.AbilityUsable("Hammer of Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hammer of Wrath", "target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),    
    }
}

Triggers.HolyPala.LightsHammer = {
    Name="Light's Hammer",
    Binding=scripty.Bindings.LightsHammer,
    Conditions={
        sc.AbilityUsable("Light's Hammer"),
        sc.LODTargetsGreaterThan(0),
    }
}

Triggers.HolyPala.RuleOfLaw = {
    Name="Rule of Law",
    Binding=scripty.Bindings.RuleOfLaw,
    Conditions={
        sc.AbilityUsable("Rule of Law"),
        Inverse(sc.UnitHasAura("player", "Rule of Law", "HELPFUL")),
    }
}

Triggers.HolyPala.DivineFavor = {
    Name="Divine Favor",
    Binding=scripty.Bindings.DivineFavor,
    Conditions={
        sc.AbilityUsable("Divine Favor"),
        Inverse(sc.UnitHasAura("player", "Divine Favor", "HELPFUL")),
    }
}

Triggers.HolyPala.Cleanse = {
    Name="Cleanse",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.Cleanse,
    Conditions={
        sc.AbilityUsable("Cleanse"),
        sc.AbilityInRange("Flash of Light", {name="purifyTarget"}),
    }
}

Triggers.Prot.CleanseAfflicted = {
    Name="Afflicted Cleanse",
    Binding=scripty.Bindings.CleanseMouseOver,
    Conditions={
        sc.AbilityUsable("Cleanse Toxins"),
        sc.UnitNameIs("mouseover", "Afflicted Soul"),
    }
}

Triggers.HolyPala.CleanseAfflicted = {
    Name="Afflicted Cleanse",
    Binding=scripty.Bindings.CleanseMouseOver,
    Conditions={
        sc.AbilityUsable("Cleanse"),
        sc.UnitNameIs("mouseover", "Afflicted Soul"),
    }
}

Triggers.HolyPala.BeaconOfLight = {
    Name="Beacon of Light",
    Focus={name="bestBolightTarget"},
    Binding=scripty.Bindings.BeaconOfLight,
    Conditions={
        sc.AbilityUsable("Beacon of Light"),
        sc.AbilityInRange("Flash of Light", {name="bestBolightTarget"}),
        Inverse(sc.UnitHasAura("player", "Beacon of Light", "PLAYER | HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Beacon of Faith", "PLAYER | HELPFUL")),
    }
}

Triggers.HolyPala.BeaconOfFaith = {
    Name="Beacon of Faith",
    Focus={name="bestBofaithTarget"},
    Binding=scripty.Bindings.BeaconOfFaith,
    Conditions={
        sc.AbilityUsable("Beacon of Faith"),
        sc.AbilityInRange("Flash of Light", {name="bestBofaithTarget"}),
    }
}

--ProtPala
Triggers.ProtPala.Intercession = {
    Name="Intercession",
    Focus={name="deadGuyTarget"},
    Binding=scripty.Bindings.Intercession,
    Conditions={
        sc.AbilityUsable("Intercession"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3),
        sc.IsUnitInCombat("player"),
        Inverse(sc.AbilityWasCastRecently("Intercession", 30)),
    }
}
Triggers.ProtPala.BastionOfLight = {
    Name="Bastion of Light",
    Binding=scripty.Bindings.BastionOfLight,
    Conditions={
        sc.AbilityUsable("Bastion of Light"),
        sc.UnitAttackable("target"),
    }
}
Triggers.ProtPala.CleanseToxins = {
    Name="Detox me",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.CleanseToxins,
    Conditions={
        sc.AbilityUsable("Cleanse Toxins"),
        sc.AbilityInRange("Cleanse Toxins", {name="purifyTarget"}),
    }
}
Triggers.ProtPala.BlessingOfSacrifice = {
    Name="Blessing Of Sacrifice",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.BlessingOfSacrifice,
    Conditions={
        sc.AbilityUsable("Blessing of Sacrifice"),
        sc.UnitHPDamageAtLeastTakenSince({name="bestHealTarget"}, 80, 3),
        Inverse(sc.UnitHPLessThan("player", 75)),
        sc.AbilityInRange("Blessing of Sacrifice", {name="bestHealTarget"}),
        Inverse(sc.UnitIsUnit("player", {name="bestHealTarget"})),
    }
}

Triggers.ProtPala.BlessingOfFreedom = {
    Name="Blessing Of Freedom",
    Focus="player",
    Binding=scripty.Bindings.BlessingOfFreedom,
    Conditions={
        sc.AbilityUsable("Blessing of Freedom"),
        sc.PlayerMoving(),
        sc.UnitSpeedLessThan("player", 4),
    }
}

Triggers.ProtPala.BlessingOfProtection = {
    Name="Blessing Of Protection",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.BlessingOfProtection,
    Conditions={
        sc.AbilityUsable("Blessing of Protection"),
        sc.UnitHPDamagePhysicalAtLeastTakenSince({name="bestHealTarget"}, 30, 4),
        sc.UnitHPLessThan({name="bestHealTarget"}, 75),
        Inverse(sc.UnitIsUnit("player", {name="bestHealTarget"})),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Forbearance", "HARMFUL")),
        sc.AbilityInRange("Blessing of Protection", {name="bestHealTarget"}),
    }
}

Triggers.ProtPala.BlessingOfSpellwarding = {
    Name="Blessing of Spellwarding",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.BlessingOfSpellwarding,
    Conditions={
        sc.AbilityUsable("Blessing of Spellwarding"),
        sc.UnitHPDamageMagicAtLeastTakenSince({name="bestHealTarget"}, 30, 4),
        sc.UnitHPLessThan({name="bestHealTarget"}, 75),
        Inverse(sc.UnitIsUnit("player", {name="bestHealTarget"})),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Forbearance", "HARMFUL")),
        sc.AbilityInRange("Blessing of Spellwarding", {name="bestHealTarget"}),
    } 
}

Triggers.ProtPala.BlindingLight = {
    Name="Blinding Light",
    Binding=scripty.Bindings.BlindingLight,
    Conditions={
        sc.AbilityUsable("Blinding Light"),
        sc.NumberOf10YardEnemiesGreaterThan(0),
        Inverse(sc.AbilityWasCastRecently("Hammer of Justice", 15)),
        Inverse(sc.AbilityWasCastRecently("Eye of Tyr", 15)),
    }
}

Triggers.ProtPala.EyeOfTyr = {
    Name="Eye of Tyr",
    Binding=scripty.Bindings.EyeOfTyr,
    Conditions={
        sc.AbilityUsable("Eye of Tyr"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.ProtPala.ConsecrationLow = {
    Name="Consecration",
    Binding=scripty.Bindings.Consecration,
    Conditions={
        sc.AbilityUsable("Consecration"),
        Inverse(sc.UnitHasAura("player", "Consecration", "HELPFUL")),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.ProtPala.Consecration = {
    Name="Consecration",
    Binding=scripty.Bindings.Consecration,
    Conditions={
        sc.AbilityUsable("Consecration"),
        Inverse(sc.UnitHasAura("player", "Consecration", "HELPFUL")),
        sc.IsUnitInCombat("player"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.ProtPala.DevotionAura = {
    Name="Devotion Aura",
    Binding=scripty.Bindings.DevotionAura,
    Conditions={
        sc.AbilityUsable("Devotion Aura"),
        Inverse(sc.UnitHasAura("player", "Devotion Aura", "HELPFUL")),
    }
}

Triggers.ProtPala.RetributionAura = {
    Name="Retribution Aura",
    Binding=scripty.Bindings.RetributionAura,
    Conditions={
        sc.AbilityUsable("Retribution Aura"),
        Inverse(sc.UnitHasAura("player", "Retribution Aura", "HELPFUL")),
    }
}

Triggers.ProtPala.GuardianOfAncientKings = {
    Name="Guardian of Ancient Kings",
    Binding=scripty.Bindings.GuardianOfAncientKings,
    Conditions={
        sc.AbilityUsable("Guardian of Ancient Kings"),
        sc.UnitAttackable("target"),
        sc.MissingPallyDefensive(),
    }
}

Triggers.ProtPala.ArdentDefender = {
    Name="Ardent Defender",
    Binding=scripty.Bindings.ArdentDefender,
    Conditions={
        sc.AbilityUsable("Ardent Defender"),
        sc.UnitAttackable("target"),
        sc.MissingPallyDefensive(),
    }
}

Triggers.ProtPala.Sentinel = {
    Name="Sentinel",
    Binding=scripty.Bindings.Sentinel,
    Conditions={
        sc.AbilityUsable("Sentinel"),
        sc.UnitAttackable("target"),
        sc.MissingPallyDefensive(),
    }
}

Triggers.ProtPala.HolyBulwark = {
    Name="Holy Bulwark",
    Binding=scripty.Bindings.HolyBulwark,
    Conditions={
        sc.AbilityUsable("Holy Bulwark"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Holy Bulwark", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Sacred Weapon", "HELPFUL")),
    }
}

Triggers.ProtPala.HolyBulwark2 = {
    Name="Holy Bulwark",
    Binding=scripty.Bindings.HolyBulwark,
    Conditions={
        sc.AbilityUsable("Sacred Weapon"),
        sc.UnitAttackable("target"),
    }
}

Triggers.ProtPala.AvengingWrath = {
    Name="Avenging Wrath",
    Binding=scripty.Bindings.AvengingWrath,
    Conditions={
        sc.AbilityUsable("Avenging Wrath"),
        sc.UnitAttackable("target"),
        sc.MissingPallyDefensive(),
    }
}

Triggers.ProtPala.MomentOfGlory = {
    Name="Moment of Glory",
    Binding=scripty.Bindings.MomentOfGlory,
    Conditions={
        sc.AbilityUsable("Moment of Glory"),
        sc.UnitAttackable("target"),
    }
}

Triggers.ProtPala.HolyAvenger = {
    Name="Holy Avenger",
    Binding=scripty.Bindings.HolyAvenger,
    Conditions={
        sc.AbilityUsable("Holy Avenger"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Avenging Wrath", "HELPFUL")),
        Inverse(sc.AbilityWasCastRecently("Avenging Wrath", 30)),
        sc.AbilityOnCD("Avenging Wrath"),
        Inverse(sc.UnitHasAura("player", "Moment of Glory", "HELPFUL")),
        Inverse(sc.AbilityWasCastRecently("Moment of Glory", 15)),
        sc.AbilityOnCD("Moment of Glory"),
        Inverse(sc.UnitHasAura("player", "Sentinel", "HELPFUL")),
        Inverse(sc.AbilityWasCastRecently("Sentinel", 30)),
    }
}

Triggers.ProtPala.DivineShield = {
    Name="Divine Shield",
    Binding=scripty.Bindings.DivineShield,
    Conditions={
        sc.AbilityUsable("Divine Shield"),
        sc.UnitHPLessThan("player", 30),
        -- Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3)),
        Inverse(sc.UnitHasAura("player", "Forbearance", "HARMFUL")),
    }
}

Triggers.ProtPala.LayOnHandsMe = {
    Name="Lay on Hands Me",
    Focus="player",
    Binding=scripty.Bindings.LayOnHands,
    Conditions={
        sc.AbilityUsable("Lay on Hands"),
        sc.UnitHPLessThan("player", 35),
        Inverse(sc.UnitHasAura("player", "Forbearance", "HARMFUL")),
        sc.AbilityOnCD("Divine Shield"),
    }
}

Triggers.ProtPala.LayOnHands = {
    Name="Lay on Hands",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LayOnHands,
    Conditions={
        sc.AbilityUsable("Lay on Hands"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 35),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Forbearance", "HARMFUL")),
        sc.AbilityInRange("Lay on Hands", {name="bestHealTarget"}),
        Inverse(sc.UnitIsUnit("player", {name="bestHealTarget"})),
    }
}

Triggers.ProtPala.DivineSteed = {
    Name="Divine Steed",
    Binding=scripty.Bindings.DivineSteed,
    Conditions={
        function() return false end,
        -- sc.AbilityUsable("Divine Steed"),
        -- sc.PlayerMoving(),
        -- Inverse(sc.UnitHasAura("player", "Divine Steed", "HELPFUL")),
        -- sc.UnitSpeedLessThan("player", 10),
    }
}

Triggers.ProtPala.FlashOfLight = {
    Name="Flash of Light",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.FlashOfLight,
    Conditions={
        sc.AbilityUsable("Flash of Light"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 75),
        sc.AbilityInRange("Flash of Light", {name="bestHealTarget"}),
    }
}

Triggers.ProtPala.HammerOfJustice = {
    Name="Hammer of Justice",
    Binding=scripty.Bindings.HammerOfJustice,
    Conditions={
        sc.AbilityUsable("Hammer of Justice"),
        sc.AbilityInRange("Hammer of Justice", "target"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
    }
}

Triggers.ProtPala.HammerOfWrath = {
    Name="Hammer of Wrath",
    Binding=scripty.Bindings.HammerOfWrath,
    Conditions={
        sc.AbilityUsable("Hammer of Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hammer of Wrath", "target"),
    }
}

Triggers.ProtPala.HammerOfWrath2 = {
    Name="Hammer of Wrath",
    Binding=scripty.Bindings.HammerOfWrath,
    Conditions={
        sc.AbilityUsable("Hammer of Wrath"),
        sc.UnitAttackable("target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3)),    
        sc.AbilityInRange("Hammer of Wrath", "target"),
    }
}

Triggers.ProtPala.HandOfReckoning = {
    Name="Hand of Reckoning",
    Binding=scripty.Bindings.HandOfReckoning,
    Conditions={
        sc.AbilityUsable("Hand of Reckoning"),
        sc.UnitAttackable("target"),
        sc.ProtPalaUnitNeedsTaunt("target"),
        sc.AbilityInRange("Hand of Reckoning", "target"),
    }
}

Triggers.ProtPala.Judgment = {
    Name="Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Judgment", "target"),
    }
}

Triggers.ProtPala.Judgment2 = {
    Name="Judgment",
    Binding=scripty.Bindings.Judgment,
    Conditions={
        sc.AbilityUsable("Judgment"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Judgment", "target"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3)),
    }
}

Triggers.ProtPala.Rebuke = {
    Name="Rebuke",
    Binding=scripty.Bindings.Rebuke,
    Conditions={
        sc.AbilityUsable("Rebuke"),
        sc.TargetIsReadyForInterrupt("target"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.ProtPala.ShieldOfTheRighteous = {
    Name="Shield of the Righteous",
    Binding=scripty.Bindings.ShieldOfTheRighteous,
    Conditions={
        sc.AbilityUsable("Shield of the Righteous"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5),
    }
}

Triggers.ProtPala.ShieldOfTheRighteous2 = {
    Name="Shield of the Righteous",
    Binding=scripty.Bindings.ShieldOfTheRighteous,
    Conditions={
        sc.AbilityUsable("Shield of the Righteous"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5),
        sc.AbilityUsable("Judgment"),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.ProtPala.ShieldOfTheRighteous3 = {
    Name="Shield of the Righteous",
    Binding=scripty.Bindings.ShieldOfTheRighteous,
    Conditions={
        sc.AbilityUsable("Shield of the Righteous"),
        sc.AbilityUsable("Judgment"),
        Inverse(sc.UnitHasAura("player", "Shield of the Righteous", "HELPFUL")),
    }
}

-- Triggers.ProtPala.ShieldOfTheRighteousBulwark = {
--     Name="Shield of the Righteous",
--     Binding=scripty.Bindings.ShieldOfTheRighteous,
--     Conditions={
--         sc.AbilityUsable("Shield of the Righteous"),
--         sc.UnitAttackable("target"),
--         sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3),
--         sc.UnitHasAura("player", "Bulwark of Righteous Fury", "HELPFUL"),
--     }
-- }

Triggers.ProtPala.ShieldOfTheRighteousDusk = {
    Name="Shield of the Righteous",
    Binding=scripty.Bindings.ShieldOfTheRighteous,
    Conditions={
        sc.AbilityUsable("Shield of the Righteous"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 4)),
        sc.UnitAuraDurationLessThan("player", "Blessing of Dusk", 3, "HELPFUL"),
    }
}

Triggers.ProtPala.ShieldOfTheRighteousRedoubt = {
    Name="Shield of the Righteous",
    Binding=scripty.Bindings.ShieldOfTheRighteous,
    Conditions={
        sc.AbilityUsable("Shield of the Righteous"),
        sc.UnitAuraDurationLessThan("player", "Redoubt", 2, "HELPFUL"),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.ProtPala.ShieldOfTheRighteousMaintain = {
    Name="Shield of the Righteous",
    Binding=scripty.Bindings.ShieldOfTheRighteous,
    Conditions={
        sc.AbilityUsable("Shield of the Righteous"),
        sc.UnitAuraDurationLessThan("player", "Shield of the Righteous", 2, "HELPFUL"),
        sc.NumberOfEnemiesGreaterThan(0),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.ProtPala.WordOfGlory = {
    Name="Word of Glory",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 30),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
    }
}

Triggers.ProtPala.Seraphim = {
    Name="Seraphim",
    Binding=scripty.Bindings.Seraphim,
    Conditions={
        sc.AbilityUsable("Seraphim"),
    }
}

Triggers.ProtPala.WordOfGloryLowPriority = {
    Name="Word of Glory Low",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5),
        sc.AbilityInRange("Word of Glory", {name="bestHealTarget"}),
    }
}

Triggers.ProtPala.WordOfGloryPlayerFirst = {
    Name="Word of Glory Player",
    Focus="player",
    Binding=scripty.Bindings.WordOfGlory,
    Conditions={
        sc.AbilityUsable("Word of Glory"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.ProtPala.AvengersShieldInterrupt = {
    Name="Avenger's Shield",
    Binding=scripty.Bindings.AvengersShield,
    Conditions={
        sc.AbilityUsable("Avenger's Shield"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Avenger's Shield", "target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.ProtPala.AvengersShieldMoment = {
    Name="Avenger's Shield",
    Binding=scripty.Bindings.AvengersShield,
    Conditions={
        sc.AbilityUsable("Avenger's Shield"),
        sc.AbilityInRange("Avenger's Shield", "target"),
        sc.UnitHasAura("player", "Moment of Glory", "HELPFUL"),
        sc.UnitAttackable("target"),
    }
}

Triggers.ProtPala.AvengersShieldBulwark = {
    Name="Avenger's Shield",
    Binding=scripty.Bindings.AvengersShield,
    Conditions={
        sc.AbilityUsable("Avenger's Shield"),
        sc.AbilityInRange("Avenger's Shield", "target"),
        Inverse(sc.UnitHasAura("player", "Bulwark of Righteous Fury", "HELPFUL")),
        sc.UnitAttackable("target"),
    }
}

Triggers.ProtPala.AvengersShield = {
    Name="Avenger's Shield",
    Binding=scripty.Bindings.AvengersShield,
    Conditions={
        sc.AbilityUsable("Avenger's Shield"),
        sc.AbilityInRange("Avenger's Shield", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.ProtPala.BlessedHammer = {
    Name="Blessed Hammer",
    Binding=scripty.Bindings.BlessedHammer,
    Conditions={
        sc.AbilityUsable("Blessed Hammer"),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.ProtPala.BlessedHammer2 = {
    Name="Blessed Hammer",
    Binding=scripty.Bindings.BlessedHammer,
    Conditions={
        sc.AbilityUsable("Blessed Hammer"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 3)),
    }
}

Triggers.ProtPala.BlessedHammer3 = {
    Name="Blessed Hammer",
    Binding=scripty.Bindings.BlessedHammer,
    Conditions={
        sc.AbilityUsable("Blessed Hammer"),
        sc.IsUnitInCombat("player"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_HOLYPOWER, 5)),
    }
}

Triggers.ProtPala.DivineToll = {
    Name="Divine Toll",
    Binding=scripty.Bindings.DivineTollAttack,
    Conditions={
        sc.AbilityUsable("Divine Toll"),
        sc.AbilityInRange("Avenger's Shield", "target"),
        sc.UnitAttackable("target"),
    }
}

--Affliction
Triggers.Affliction.BurningRush = {
    Name="Burning Rush",
    Binding=scripty.Bindings.BurningRush,
    Conditions={
        sc.AbilityUsable("Burning Rush"),
        sc.PlayerBeenMoving(),
        Inverse(sc.UnitHPLessThan("player", 90)),
        Inverse(sc.UnitHasAura("player", "Burning Rush", "HELPFUL")),
        Inverse(sc.IsUnitInCombat("player")),
    }
}

Triggers.Affliction.BurningRushCancel = {
    Name="Burning Rush Cancel",
    Binding=scripty.Bindings.BurningRushCancel,
    Conditions={
        Inverse(sc.PlayerMoving()),
        sc.UnitHasAura("player", "Burning Rush", "HELPFUL"),
    }
}

Triggers.Affliction.BurningRushCancel2 = {
    Name="Burning Rush Cancel2",
    Binding=scripty.Bindings.BurningRushCancel,
    Conditions={
        sc.UnitHPLessThan("player", 75),
        sc.UnitHasAura("player", "Burning Rush", "HELPFUL"),
    }
}

Triggers.Affliction.SpellLock = {
    Name="Spell Lock",
    Binding=scripty.Bindings.SpellLock,
    Conditions={
        sc.AbilityUsable("Spell Lock"),
        sc.TargetIsReadyForInterrupt(),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.CorruptionLow = {
    Name="CorruptionLow",
    Binding=scripty.Bindings.Corruption,
    Conditions={
        sc.AbilityUsable("Corruption"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Corruption", 4, "PLAYER | HARMFUL"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.Corruption = {
    Name="Corruption",
    Binding=scripty.Bindings.Corruption,
    Conditions={
        sc.AbilityUsable("Corruption"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Corruption", "PLAYER | HARMFUL")),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.AgonyRefresh = {
    Name="Agony",
    Binding=scripty.Bindings.Agony,
    Conditions={
        sc.AbilityUsable("Agony"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Agony", 5, "PLAYER | HARMFUL"),
        sc.UnitHasAura("target", "Agony", "PLAYER | HARMFUL"),
        sc.AbilityInRange("Unstable Affliction", "target"),
        Inverse(sc.UnitLowHealthShit("target")),
    }
}

Triggers.Affliction.Agony = {
    Name="Agony",
    Binding=scripty.Bindings.Agony,
    Conditions={
        sc.AbilityUsable("Agony"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Agony", "PLAYER | HARMFUL")),
        sc.AbilityInRange("Unstable Affliction", "target"),
        Inverse(sc.UnitLowHealthShit("target")),
    }
}

Triggers.Affliction.AgonyLow = {
    Name="Agony",
    Binding=scripty.Bindings.Agony,
    Conditions={
        sc.AbilityUsable("Agony"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Agony", 5, "PLAYER | HARMFUL"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.CreateHealthStone = {
    Name="Create Healthstone",
    Binding=scripty.Bindings.CreateHealthStone,
    Conditions={
        sc.AbilityUsable("Create Healthstone"),
    }
}

Triggers.Affliction.CurseOfExhaustion = {
    Name="Slow",
    Binding=scripty.Bindings.CurseOfExhaustion,
    Conditions={
        sc.AbilityUsable("Curse of Exhaustion"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Curse of Exhaustion", "PLAYER | HARMFUL")),
        sc.UnitIsUnit("targettarget", "player"),
        sc.UnitIsMoving("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.DarkPact = {
    Name="Dark Pact",
    Binding=scripty.Bindings.DarkPact,
    Conditions={
        sc.AbilityUsable("Dark Pact"),
        sc.UnitHPLessThan("player", 80),
        sc.UnitHPDamageAtLeastTakenSince("player", 1, 3),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.Affliction.DrainLife = {
    Name="Drain Life",
    Binding=scripty.Bindings.DrainLife,
    Conditions={
        sc.AbilityUsable("Drain Life"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("player", 50),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 25),
        sc.PlayerResourceMinimum(SOPHIE_POWER_SOULSHARDS, 1),
        sc.AbilityInRange("Drain Life", "target"),
    }
}

Triggers.Affliction.DrainLife2 = {
    Name="Drain Life",
    Binding=scripty.Bindings.DrainLife,
    Conditions={
        sc.AbilityUsable("Drain Life"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("player", 90),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 25),
        sc.PlayerResourceMinimum(SOPHIE_POWER_SOULSHARDS, 1),
        sc.AbilityInRange("Drain Life", "target"),
    }
}

Triggers.Affliction.DrainSoulProc = {
    Name="Nightfall",
    Binding=scripty.Bindings.DrainSoul,
    Conditions={
        sc.AbilityUsable("Drain Soul"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Nightfall", "PLAYER | HELPFUL"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.DrainSoul = {
    Name="Drain Soul",
    Binding=scripty.Bindings.DrainSoul,
    Conditions={
        sc.AbilityUsable("Drain Soul"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.InquisitorsGaze = {
    Name="Inquisitor's Gaze",
    Binding=scripty.Bindings.InquisitorsGaze,
    Conditions={
        sc.AbilityUsable("Inquisitor's Gaze"),
        Inverse(sc.UnitHasAura("player", "Inquisitor's Gaze", "HELPFUL")),
    }
}

Triggers.Affliction.MortalCoil = {
    Name="Mortal Coil",
    Binding=scripty.Bindings.MortalCoil,
    Conditions={
        sc.AbilityUsable("Mortal Coil"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("player", 50),
        sc.EnemyUnitWithin("target", 20),
    }
}

Triggers.Affliction.SummonFelhunter = {
    Name="Summon Felhunter",
    Binding=scripty.Bindings.SummonFelhunter,
    Conditions={
        sc.AbilityUsable("Summon Felhunter"),
        sc.NoPetOrDemonicSacrificeBuff(),
    }
}

Triggers.Affliction.GrimoireOfSacrifice = {
    Name="Grimoire of Sacrifice",
    Binding=scripty.Bindings.GrimoireOfSacrifice,
    Conditions={
        sc.AbilityUsable("Grimoire of Sacrifice"),
        Inverse(sc.UnitHasAura("player", "Grimoire of Sacrifice", "HELPFUL")),
        sc.LivePet(),
    }
}

Triggers.Affliction.Deathbolt = {
    Name="Deathbolts",
    Binding=scripty.Bindings.Deathbolt,
    Conditions={
        sc.AbilityUsable("Deathbolt"),
        sc.UnitAttackable("target"),
        sc.UnitHasAuraOrSpell("target", "Agony", "PLAYER | HARMFUL"),
        sc.UnitHasAuraOrSpell("target", "Corruption", "PLAYER | HARMFUL"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.BaneOfShadows = {
    Name="Bane of Shadows",
    Binding=scripty.Bindings.BaneOfShadows,
    Conditions={
        sc.AbilityUsable("Bane of Shadows"),
        sc.UnitAttackable("target"),
        sc.UnitHasAuraOrSpell("target", "Agony", "PLAYER | HARMFUL"),
        sc.UnitHasAuraOrSpell("target", "Corruption", "PLAYER | HARMFUL"),
    }
}

Triggers.Affliction.RapidContagion = {
    Name="Rapid Contagion",
    Binding=scripty.Bindings.RapidContagion,
    Conditions={
        sc.AbilityUsable("Rapid Contagion"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("target", "Agony", "PLAYER | HARMFUL"),
        sc.UnitHasAura("target", "Corruption", "PLAYER | HARMFUL"),
        sc.UnitHasAura("target", "Bane of Shadows", "PLAYER | HARMFUL"),
    }
}

Triggers.Affliction.Soulrot = {
    Name="Soul Rot",
    Binding=scripty.Bindings.Soulrot,
    Conditions={
        sc.AbilityUsable("Soul Rot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.Haunt = {
    Name="Haunt",
    Binding=scripty.Bindings.Haunt,
    Conditions={
        sc.AbilityUsable("Haunt"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
        Inverse(sc.UnitHasAuraOrSpell("target", "Haunt", "PLAYER | HARMFUL")),
    }
}

Triggers.Affliction.UnstableAffliction = {
    Name="Unstable Affliction",
    Binding=scripty.Bindings.UnstableAffliction,
    Conditions={
        sc.AbilityUsable("Unstable Affliction"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
        Inverse(sc.UnitHasAuraOrSpell("target", "Unstable Affliction", "PLAYER | HARMFUL")),
        Inverse(sc.UnstableRecentlyTicked()),
        Inverse(sc.AbilityWasCastRecently("Unstable Affliction", 3)),
    }
}

Triggers.Affliction.PhantomSingularity = {
    Name="Phantom Singularity",
    Binding=scripty.Bindings.PhantomSingularity,
    Conditions={
        sc.AbilityUsable("Phantom Singularity"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Unstable Affliction", "target"),
    }
}

Triggers.Affliction.MaleficRaptureProc = {
    Name="Instant Malefic",
    Binding=scripty.Bindings.MaleficRapture,
    Conditions={
        sc.AbilityUsable("Malefic Rapture"),
        sc.UnitAttackable("target"),
        sc.ActiveDotCountGreaterThan(2),
        sc.UnitHasAura("player", "Tormented Crescendo", "HELPFUL"),
    }
}

Triggers.Affliction.MaleficRapture = {
    Name="Malefic",
    Binding=scripty.Bindings.MaleficRapture,
    Conditions={
        sc.AbilityUsable("Malefic Rapture"),
        sc.UnitAttackable("target"),
        sc.ActiveDotCountGreaterThan(2),
    }
}

Triggers.Affliction.SoulSwap = {
    Name="Soul Swap",
    Binding=scripty.Bindings.SoulSwap,
    Conditions={
        sc.AbilityUsable("Soul Swap"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Unstable Affliction", 4, "HARMFUL | PLAYER"),
    }
}

Triggers.Affliction.SoulTap = {
    Name="Soul Tap",
    Binding=scripty.Bindings.SoulTap,
    Conditions={
        sc.AbilityUsable("Soul Tap"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_SOULSHARDS, 5)),
    }
}

Triggers.Affliction.MaleficRaptureCap = {
    Name="Malefic to avoid cap",
    Binding=scripty.Bindings.MaleficRapture,
    Conditions={
        sc.AbilityUsable("Malefic Rapture"),
        sc.UnitAttackable("target"),
        sc.ActiveDotCountGreaterThan(2),
        sc.PlayerResourceMinimum(SOPHIE_POWER_SOULSHARDS, 4),
    }
}

--Destro
Triggers.Destro.Fear = {
    Name="Fear",
    Binding=scripty.Bindings.Fear,
    Conditions={
        sc.AbilityUsable("Fear"),
        sc.UnitAttackable("target"),
        sc.UnitIsPlayer("target"),
        Inverse(sc.UnitHPLessThan("target", 90)),
        sc.EnemyUnitWithin("target", 20),
        Inverse(sc.UnitHasAuraOrSpell("target", "Fear", "PLAYER | HARMFUL")),
        Inverse(sc.AbilityWasCastRecently("Fear", 10)),
    }
}

Triggers.Destro.Fear2 = {
    Name="Fear",
    Binding=scripty.Bindings.Fear,
    Conditions={
        sc.AbilityUsable("Fear"),
        sc.UnitAttackable("target"),
        sc.UnitIsUnit("player", "targettarget"),
        Inverse(sc.UnitHPLessThan("target", 90)),
        sc.EnemyUnitWithin("target", 20),
        Inverse(sc.UnitHasAuraOrSpell("target", "Fear", "PLAYER | HARMFUL")),
        Inverse(sc.AbilityWasCastRecently("Fear", 10)),
    }
}

Triggers.Destro.DevourMagic = {
    Name="Devour Magic",
    Binding=scripty.Bindings.DevourMagic,
    Conditions={
        sc.AbilityUsable("Devour Magic"),
        sc.UnitAttackable("target"),
        sc.UnitNeedsPurge("target"),
    }
}

Triggers.Destro.NetherWard = {
    Name="Nether Ward",
    Binding=scripty.Bindings.NetherWard,
    Conditions={
        sc.AbilityUsable("Nether Ward"),
        sc.UnitAttackable("target"),
        sc.UnitHPDamageMagicAtLeastTakenSince("player", 1, 2),
    }
}

Triggers.Destro.Incinerate = {
    Name="Incinerate",
    Binding=scripty.Bindings.Incinerate,
    Conditions={
        sc.AbilityUsable("Incinerate"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Destro.IncinerateAoe = {
    Name="Incinerate",
    Binding=scripty.Bindings.Incinerate,
    Conditions={
        sc.AbilityUsable("Incinerate"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(3),
    }
}

Triggers.Destro.Immolate = {
    Name="Immolate",
    Binding=scripty.Bindings.Immolate,
    Conditions={
        sc.AbilityUsable("Immolate"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAuraOrSpell("target", "Immolate", "PLAYER | HARMFUL")),
    }
}

Triggers.Destro.Conflagrate = {
    Name="Conflagrate",
    Binding=scripty.Bindings.Conflagrate,
    Conditions={
        sc.AbilityUsable("Conflagrate"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Conflagrate", "PLAYER | HARMFUL")),
    }
}

Triggers.Destro.ChaosBoltAvoidCap = {
    Name="Chaos Bolt",
    Binding=scripty.Bindings.ChaosBolt,
    Conditions={
        sc.AbilityUsable("Chaos Bolt"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_SOULSHARDS, 4),
    }
}

Triggers.Destro.ChaosBoltBackDraft = {
    Name="Chaos Bolt",
    Binding=scripty.Bindings.ChaosBolt,
    Conditions={
        sc.AbilityUsable("Chaos Bolt"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Backdraft", "HELPFUL"),
    }
}

Triggers.Destro.ChaosBoltChain = {
    Name="Chaos Bolt",
    Binding=scripty.Bindings.ChaosBolt,
    Conditions={
        sc.AbilityUsable("Chaos Bolt"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Madness of the Azj'Aqir", "HELPFUL"),
    }
}

Triggers.Destro.ChaosBolt = {
    Name="Chaos Bolt",
    Binding=scripty.Bindings.ChaosBolt,
    Conditions={
        sc.AbilityUsable("Chaos Bolt"),
        sc.UnitAttackable("target"),
    }
}

--Demo
Triggers.Demo.FearSpiteful = {
    Name="Fear",
    Binding=scripty.Bindings.Fear,
    Conditions={
        sc.AbilityUsable("Fear"),
        sc.UnitAttackable("target"),
        sc.UnitIsUnit("player", "targettarget"),
        sc.EnemyUnitWithin("target", 20),
        Inverse(sc.UnitHasAuraOrSpell("target", "Fear", "PLAYER | HARMFUL")),
    }
}

Triggers.Demo.Soulstone = {
    Name="Soulstone",
    Binding=scripty.Bindings.Soulstone,
    Conditions={
        sc.AbilityUsable("Soulstone"),
    }
}
Triggers.Demo.DrainLife = {
    Name="Drain Life",
    Binding=scripty.Bindings.DrainLife,
    Conditions={
        sc.AbilityUsable("Drain Life"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("player", 40),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 25),
    }
}
Triggers.Demo.DrainLife2 = {
    Name="Drain Life",
    Binding=scripty.Bindings.DrainLife,
    Conditions={
        sc.AbilityUsable("Drain Life"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("player", 50),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 25),
    }
}

Triggers.Demo.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellDemo(),
    }
}
Triggers.Demo.HealthFunnel = {
    Name="Health Funnel",
    Binding=scripty.Bindings.HealthFunnel,
    Conditions={
        sc.AbilityUsable("Health Funnel"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_SOULSHARDS, 1),
        sc.UnitHPLessThan("pet", 50),
        Inverse(sc.UnitHPLessThan("player", 50)),
    }
}

Triggers.Demo.SummonFelguard = {
    Name="Summon Felguard",
    Binding=scripty.Bindings.SummonFelguard,
    Conditions={
        sc.AbilityUsable("Summon Felguard"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_SOULSHARDS, 1),
        sc.NoPetOrDemonicSacrificeBuff(),
    }
}

Triggers.Demo.FelDomination = {
    Name="Fel dom",
    Binding=scripty.Bindings.FelDomination,
    Conditions={
        sc.AbilityUsable("Fel Domination"),
        sc.NoPetOrDemonicSacrificeBuff(),
    }
}

Triggers.Demo.ShadowBolt = {
    Name="Shadow Bolt",
    Binding=scripty.Bindings.ShadowBolt,
    Conditions={
        sc.AbilityUsable("Shadow Bolt"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
    }
}

Triggers.Demo.GrimoireFelguard = {
    Name="Grimoire Felguard",
    Binding=scripty.Bindings.GrimoireFelguard,
    Conditions={
        sc.AbilityUsable("Grimoire: Felguard"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Demo.CallDreadstalkers = {
    Name="Call Dreadstalkers",
    Binding=scripty.Bindings.CallDreadstalkers,
    Conditions={
        sc.AbilityUsable("Call Dreadstalkers"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_SOULSHARDS, 2),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
    }
}

Triggers.Demo.PetAttack = {
    Name="PA",
    Binding=scripty.Bindings.PetAttack,
    Conditions={
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAttackable("pettarget")),
    }
}

Triggers.Demo.PetAttack2 = {
    Name="PA",
    Binding=scripty.Bindings.PetAttack,
    Conditions={
        sc.UnitAttackable("target"),
        Inverse(sc.UnitIsUnit("pettarget", "target")),
        sc.UnitIsUnit("targettarget", "player"),
    }
}

Triggers.Demo.HandOfGuldan = {
    Name="Hand of Guldan",
    Binding=scripty.Bindings.HandOfGuldan,
    Conditions={
        sc.AbilityUsable("Hand of Gul'dan"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_SOULSHARDS, 3),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
    }
}

Triggers.Demo.Implosion = {
    Name="Implosion",
    Binding=scripty.Bindings.Implosion,
    Conditions={
        sc.AbilityUsable("Implosion"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
        Inverse(sc.UnitHasAura("player", "Demonic Power", "HELPFUL")),
        Inverse(sc.AbilityWasCastRecently("Implosion", 10)),
        sc.MultiTargetEnemiesGreaterThan(1),
    }
}

Triggers.Demo.Implosion2 = {
    Name="Implosion",
    Binding=scripty.Bindings.Implosion,
    Conditions={
        sc.AbilityUsable("Implosion"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
        Inverse(sc.AbilityWasCastRecently("Implosion", 10)),
        sc.MultiTargetEnemiesGreaterThan(2),
    }
}

Triggers.Demo.Implosion3 = {
    Name="Implosion",
    Binding=scripty.Bindings.Implosion,
    Conditions={
        sc.AbilityUsable("Implosion"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
        Inverse(sc.AbilityWasCastRecently("Implosion", 10)),
        sc.MultiTargetEnemiesGreaterThan(4),
    }
}

Triggers.Demo.SoulStrike = {
    Name="Soul Strike",
    Binding=scripty.Bindings.SoulStrike,
    Conditions={
        sc.AbilityUsable("Soul Strike"),
        sc.UnitAttackable("pettarget"),
    }
}

Triggers.Demo.DemonicStrength = {
    Name="Demonic Strength",
    Binding=scripty.Bindings.DemonicStrength,
    Conditions={
        sc.AbilityUsable("Demonic Strength"),
        sc.UnitAttackable("pettarget"),
    }
}

Triggers.Demo.PowerSiphon = {
    Name="Power Siphon",
    Binding=scripty.Bindings.PowerSiphon,
    Conditions={
        sc.AbilityUsable("Power Siphon"),
        sc.IsUnitInCombat("player"),
        Inverse(sc.UnitAuraCountLessThan("player", "Demonic Core", 4, "HELPFUL")),
    }
}

Triggers.Demo.SummonDemonicTyrant = {
    Name="Summon Demonic Tyrant",
    Binding=scripty.Bindings.SummonDemonicTyrant,
    Conditions={
        sc.AbilityUsable("Summon Demonic Tyrant"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
    }
}

Triggers.Demo.NetherPortal = {
    Name="Nether Portal",
    Binding=scripty.Bindings.NetherPortal,
    Conditions={
        sc.AbilityUsable("Nether Portal"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_SOULSHARDS, 1),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
    }
}

Triggers.Demo.CurseOfExhaustion = {
    Name="Slow",
    Binding=scripty.Bindings.CurseOfExhaustion,
    Conditions={
        sc.AbilityUsable("Curse of Exhaustion"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitIsPlayer("target")),
        Inverse(sc.UnitHasAura("target", "Curse of Exhaustion", "PLAYER | HARMFUL")),
        sc.UnitIsUnit("targettarget", "player"),
        sc.UnitIsMoving("target"),
    }
}

Triggers.Demo.AmplifyCurse = {
    Name="Amplify Weakness",
    Binding=scripty.Bindings.AmplifyCurse,
    Conditions={
        sc.AbilityUsable("Amplify Curse"),
        sc.AbilityUsable("Curse of Weakness"),
        sc.UnitAttackable("target"),
        sc.UnitIsPlayer("target"),
        Inverse(sc.UnitHasAura("target", "Curse of Weakness", "PLAYER | HARMFUL")),
        sc.UnitIsUnit("targettarget", "player"),
    }
}

Triggers.Demo.CurseOfWeakness = {
    Name="Curse Of Weakness",
    Binding=scripty.Bindings.CurseOfWeakness,
    Conditions={
        sc.AbilityUsable("Curse of Weakness"),
        sc.UnitAttackable("target"),
        sc.UnitIsPlayer("target"),
        sc.UnitHasAura("player", "Amplify Curse", "HELPFUL"),
        Inverse(sc.UnitHasAura("target", "Curse of Weakness", "PLAYER | HARMFUL")),
        sc.UnitIsUnit("targettarget", "player"),
    }
}

Triggers.Demo.Demonbolt = {
    Name="Demonbolt",
    Binding=scripty.Bindings.Demonbolt,
    Conditions={
        sc.AbilityUsable("Demonbolt"),
        sc.UnitHasAura("player", "Demonic Core", "HELPFUL"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Hand of Gul'dan", "target"),
        Inverse(sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_SOULSHARDS, 5)),
    }
}

Triggers.Demo.Shadowfury = {
    Name="Shadowfury",
    Binding=scripty.Bindings.Shadowfury,
    Conditions={
        sc.AbilityUsable("Shadowfury"),
        sc.EnemyInMeleeNeedsStun(),
    }
}

Triggers.Demo.CommandDemon = {
    Name="Axe Toss",
    Binding=scripty.Bindings.CommandDemon,
    Conditions={
        sc.AbilityUsable("Axe Toss"),
        sc.UnitAttackable("target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

--
Triggers.Mist.DetoxAfflicted = {
    Name="Detox",
    Binding=scripty.Bindings.DetoxMouseOver,
    Conditions={
        sc.AbilityUsable("Detox"),
        sc.UnitNameIs("mouseover", "Afflicted Soul"),
    }
}
Triggers.Mist.ParaGhosts = {
    Name="Paralysis",
    Binding=scripty.Bindings.Paralysis,
    Conditions={
        sc.AbilityUsable("Paralysis"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
    }
}

Triggers.Mist.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellMist(),
    }
}
Triggers.Mist.RisingSunKick = {
    Name="Rising Sun Kick",
    Binding=scripty.Bindings.RisingSunKick,
    Conditions={
        sc.AbilityUsable("Rising Sun Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
    }
}

Triggers.Mist.ExpelHarm = {
    Name="Expel Harm",
    Binding=scripty.Bindings.ExpelHarm,
    Conditions={
        sc.AbilityUsable("Expel Harm"),
        sc.UnitHPLessThan("player", 85),
        Inverse(sc.MistHealingActive()),
        sc.UnitIsUnit({name="bestHealTarget"}, "player"),
    }
}

Triggers.Mist.Detox = {
    Name="Detox",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.Detox,
    Conditions={
        sc.AbilityUsable("Detox"),
    }
}

Triggers.Mist.BlackoutKick = {
    Name="Blackout Kick",
    Binding=scripty.Bindings.BlackoutKick,
    Conditions={
        sc.AbilityUsable("Blackout Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Teachings of the Monastery", 2, "HELPFUL")),
    }
}

Triggers.Mist.BlackoutKick2 = {
    Name="Blackout Kick",
    Binding=scripty.Bindings.BlackoutKick,
    Conditions={
        sc.AbilityUsable("Blackout Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Teachings of the Monastery", 3, "HELPFUL")),
    }
}

Triggers.Mist.BlackoutKickChiji = {
    Name="Blackout Kick",
    Binding=scripty.Bindings.BlackoutKick,
    Conditions={
        sc.AbilityUsable("Blackout Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.TotemIsUp(),
    }
}

Triggers.Mist.RisingSunKickChiji = {
    Name="Rising Sun Kick",
    Binding=scripty.Bindings.RisingSunKick,
    Conditions={
        sc.AbilityUsable("Rising Sun Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.TotemIsUp(),
    }
}

Triggers.Mist.TigerPalm = {
    Name="Tiger Palm",
    Binding=scripty.Bindings.TigerPalm,
    Conditions={
        sc.AbilityUsable("Tiger Palm"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.VivifyLifeCycle = {
    Name="Vivify",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        Inverse(sc.UnitHasAura("player", "Lifecycles (Enveloping Mist)", "HELPFUL")),
    }
}

Triggers.Mist.SoothingMist = {
    Name="Soothing Mist",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.SoothingMist,
    Conditions={
        sc.AbilityUsable("Soothing Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 95),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.SoothingMist2 = {
    Name="Soothing Mist",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.SoothingMist,
    Conditions={
        sc.AbilityUsable("Soothing Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 95),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Soothing Mist", "HELPFUL | PLAYER")),
    }
}

Triggers.Mist.Vivify = {
    Name="Vivify",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
    }
}

Triggers.Mist.VivifyInstant = {
    Name="Vivify Instant",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHasAura("player", "Vivacious Vivification", "HELPFUL"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 70),
    }
}

Triggers.Mist.VivifyInstantSooth = {
    Name="Vivify Instant",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.MistHealingActive(),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
    }
}

Triggers.Mist.ZenPulse = {
    Name="Zen Pulse",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.ZenPulse,
    Conditions={
        sc.AbilityUsable("Zen Pulse"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.SummonJadeSerpent = {
    Name="Jade Serpent",
    Binding=scripty.Bindings.SummonJadeSerpent,
    Conditions={
        sc.AbilityUsable("Summon Jade Serpent Statue"),
        Inverse(sc.MistHealingActive()),
        Inverse(sc.TotemIsUp()),
    }
}

Triggers.Mist.EnvelopingMist = {
    Name="Enveloping Mist Hard Cast",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.EnvelopingMist,
    Conditions={
        sc.AbilityUsable("Enveloping Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.UnitHasAuraOrSpell({name="bestHealTarget"}, "Enveloping Mist", "HELPFUL | PLAYER")),
        Inverse(sc.AbilityWasCastRecently("Enveloping Mist", 2)),
    }
}

Triggers.Mist.EnvelopingMistInstantCast = {
    Name="Enveloping Mist Instant Cast",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.EnvelopingMist,
    Conditions={
        sc.AbilityUsable("Enveloping Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Enveloping Mist", "HELPFUL | PLAYER")),
        sc.MistHealingActive(),
    }
}

Triggers.Mist.EnvelopingMistInstantCast2 = {
    Name="Enveloping Mist Instant Cast",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.EnvelopingMist,
    Conditions={
        sc.AbilityUsable("Enveloping Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        -- sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Enveloping Mist", "HELPFUL | PLAYER")),
        sc.UnitHasAura("player", "Thunder Focus Tea", "HELPFUL | PLAYER"),
    }
}

Triggers.Mist.EnvelopingMistInstantCast3 = {
    Name="Enveloping Mist Instant Cast",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.EnvelopingMist,
    Conditions={
        sc.AbilityUsable("Enveloping Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        -- sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Enveloping Mist", "HELPFUL | PLAYER")),
        sc.UnitHasAura("player", "Tea of Serenity", "HELPFUL | PLAYER"),
    }
}

Triggers.Mist.EnvelopingMistInstantCast4 = {
    Name="Enveloping Mist Instant Cast",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.EnvelopingMist,
    Conditions={
        sc.AbilityUsable("Enveloping Mist"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Enveloping Mist", "HELPFUL | PLAYER")),
        Inverse(sc.UnitAuraCountLessThan("player", "Invoke Chi-Ji the Red Crane", 3, "HELPFUL | PLAYER")),
    }
}

Triggers.Mist.EssenceFontTeaching = {
    Name="Essence Font",
    Binding=scripty.Bindings.EssenceFont,
    Conditions={
        sc.AbilityUsable("Essence Font"),
        Inverse(sc.UnitHasAura("player", "Ancient Teachings", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.Mist.EssenceFont = {
    Name="Essence Font",
    Binding=scripty.Bindings.EssenceFont,
    Conditions={
        sc.AbilityUsable("Essence Font"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 85),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 90),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.JadefireStomp = {
    Name="Jadefire Stomp",
    Binding=scripty.Bindings.JadefireStomp,
    Conditions={
        sc.AbilityUsable("Jadefire Stomp"),
        Inverse(sc.UnitHasAura("player", "Ancient Teachings", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.Mist.JadefireStomp2 = {
    Name="Jadefire Stomp",
    Binding=scripty.Bindings.JadefireStomp,
    Conditions={
        sc.AbilityUsable("Jadefire Stomp"),
        Inverse(sc.UnitHasAura("player", "Ancient Teachings", "HELPFUL")),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Mist.InvokeChijitheRedCrane = {
    Name="Invoke Chi-ji, the Red Crane",
    Binding=scripty.Bindings.InvokeChijitheRedCrane,
    Conditions={
        sc.AbilityUsable("Invoke Chi-ji, the Red Crane"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 75),
    }
}

Triggers.Mist.InvokeYulonTheJadeSerpent = {
    Name="Invoke Yu'lon, the Jade Serpent",
    Binding=scripty.Bindings.InvokeYulonTheJadeSerpent,
    Conditions={
        sc.AbilityUsable("Invoke Yu'lon, the Jade Serpent"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.ManaTea = {
    Name="Mana Tea",
    Binding=scripty.Bindings.ManaTea,
    Conditions={
        sc.AbilityUsable("Mana Tea"),
        Inverse(sc.UnitAuraCountLessThan("player", "Mana Tea", 2, "HELPFUL")),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 81)),
        Inverse(sc.UnitHPLessThan({name="bestHealTarget"}, 80)),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.ManaTea2 = {
    Name="Mana Tea",
    Binding=scripty.Bindings.ManaTea,
    Conditions={
        sc.AbilityUsable("Mana Tea"),
        Inverse(sc.UnitAuraCountLessThan("player", "Mana Tea", 4, "HELPFUL")),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 71)),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.LifeCocoon = {
    Name="Life Cocoon",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.LifeCocoon,
    Conditions={
        sc.AbilityUsable("Life Cocoon"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 35),
        Inverse(sc.UnitNameIs("mouseover", "Afflicted Soul")),

    }
}

Triggers.Mist.RenewingMist = {
    Name="Renewing Mist",
    Focus={name="bestMistTarget"},
    Binding=scripty.Bindings.RenewingMist,
    Conditions={
        sc.AbilityUsable("Renewing Mist"),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.SheilunsGift = {
    Name="Sheilun's Gift",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.SheilunsGift,
    Conditions={
        sc.AbilityUsable("Sheilun's Gift"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        Inverse(sc.AbilityWasCastRecently("Sheilun's Gift", 24)),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.Revival = {
    Name="Revival",
    Binding=scripty.Bindings.Revival,
    Conditions={
        sc.AbilityUsable("Revival"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 70),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Mist.ThunderFocusTea = {
    Name="Thunder Focus Tea",
    Binding=scripty.Bindings.ThunderFocusTea,
    Conditions={
        sc.AbilityUsable("Thunder Focus Tea"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        Inverse(sc.UnitHasAura("player", "Thunder Focus Tea", "HELPFUL")),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Enveloping Mist", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Invoke Chi-Ji the Red Crane", "HELPFUL | PLAYER")),
    }
}

--Brew
Triggers.Brew.Detox = {
    Name="Detox",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.Detox,
    Conditions={
        sc.AbilityUsable("Detox"),
        Inverse(sc.MistHealingActive()),
    }
}
Triggers.Brew.RingOfPeace = {
    Name="Ring of Peace",
    Binding=scripty.Bindings.RingOfPeace,
    Conditions={
        function() return false end,
    }
}
Triggers.Brew.Provoke = {
    Name="Provoke",
    Binding=scripty.Bindings.Provoke,
    Conditions={
        sc.AbilityUsable("Provoke"),
        sc.UnitAttackable("target"),
        sc.ProtPalaUnitNeedsTaunt("target"),
    }
}
Triggers.Brew.KegSmash = {
    Name="Keg Smash",
    Binding=scripty.Bindings.KegSmash,
    Conditions={
        sc.AbilityUsable("Keg Smash"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 10),
    }
}
Triggers.Brew.FortifyingBrew = {
    Name="Fortifying Brew",
    Binding=scripty.Bindings.FortifyingBrew,
    Conditions={
        sc.AbilityUsable("Fortifying Brew"),
        sc.UnitHPLessThan("player", 35),
    }
}
Triggers.Brew.HealingElixir = {
    Name="Healing Elixir",
    Binding=scripty.Bindings.HealingElixir,
    Conditions={
        sc.AbilityUsable("Healing Elixir"),
        sc.UnitHPLessThan("player", 65),
    }
}
Triggers.Brew.PurifyingBrew = {
    Name="Purifying Brew",
    Binding=scripty.Bindings.PurifyingBrew,
    Conditions={
        sc.AbilityUsable("Purifying Brew"),
        sc.StaggerGreaterThan(120),
        Inverse(sc.UnitHasAura("player", "Celestial Brew", "HELPFUL")),
        sc.AbilityOnCD("Celestial Brew"),
    }
}
Triggers.Brew.PurifyingBrew2 = {
    Name="Purifying Brew",
    Binding=scripty.Bindings.PurifyingBrew,
    Conditions={
        sc.AbilityUsable("Purifying Brew"),
        sc.SpellChargesGreaterThan("Purifying Brew", 1),
        sc.StaggerGreaterThan(0),
    }
}

Triggers.Brew.PurifyingBrew3 = {
    Name="Purifying Brew",
    Binding=scripty.Bindings.PurifyingBrew,
    Conditions={
        sc.AbilityUsable("Purifying Brew"),
        sc.UnitHPLessThan("player", 40),
        Inverse(sc.UnitHasAura("player", "Celestial Brew", "HELPFUL")),
        sc.StaggerGreaterThan(40),
    }
}

Triggers.Brew.CelestialBrew = {
    Name="Celestial Brew",
    Binding=scripty.Bindings.CelestialBrew,
    Conditions={
        sc.AbilityUsable("Celestial Brew"),
        sc.StaggerGreaterThan(100),
        sc.IsUnitInCombat("player"),
        Inverse(sc.UnitAuraCountLessThan("player", "Purified Chi", 4, "HELPFUL")),
    }
}

Triggers.Brew.CelestialBrew2 = {
    Name="Celestial Brew",
    Binding=scripty.Bindings.CelestialBrew,
    Conditions={
        sc.AbilityUsable("Celestial Brew"),
        sc.UnitHPLessThan("player", 40),
        Inverse(sc.UnitAuraCountLessThan("player", "Purified Chi", 4, "HELPFUL")),
    }
}

Triggers.Brew.BonedustBrew = {
    Name="Bonedust Brew",
    Binding=scripty.Bindings.BonedustBrew,
    Conditions={
        sc.AbilityUsable("Bonedust Brew"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Brew.BlackOxBrew = {
    Name="Black Ox Brew",
    Binding=scripty.Bindings.BlackOxBrew,
    Conditions={
        sc.AbilityUsable("Black Ox Brew"),
        sc.SpellChargesGreaterThan("Purifying Brew", 0),
        Inverse(sc.AbilityCDLessThan("Celestial Brew", 2)),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_ENERGY, 30)),
    }
}

Triggers.Brew.ExpelHarm = {
    Name="Expel Harm",
    Binding=scripty.Bindings.ExpelHarm,
    Conditions={
        sc.AbilityUsable("Expel Harm"),
        sc.UnitHPLessThan("player", 85),
    }
}

Triggers.Brew.SpinningCraneKick = {
    Name="Spinning Crane Kick",
    Binding=scripty.Bindings.SpinningCraneKick,
    Conditions={
        sc.AbilityUsable("Spinning Crane Kick"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(1),
        Inverse(sc.UnitHPLessThan("player", 95)),
    }
}
Triggers.Brew.SpinningCraneKickLow = {
    Name="Spinning Crane Kick",
    Binding=scripty.Bindings.SpinningCraneKick,
    Conditions={
        sc.AbilityUsable("Spinning Crane Kick"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(4),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Brew.BlackoutKick = {
    Name="Blackout Kick",
    Binding=scripty.Bindings.BlackoutKick,
    Conditions={
        sc.AbilityUsable("Blackout Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Brew.BlackOx = {
    Name="Invoke Niuzao, the Black Ox",
    Binding=scripty.Bindings.BlackOx,
    Conditions={
        sc.AbilityUsable("Invoke Niuzao, the Black Ox"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Brew.DampenHarm = {
    Name="Dampen Harm",
    Binding=scripty.Bindings.DampenHarm,
    Conditions={
        sc.AbilityUsable("Dampen Harm"),
        sc.UnitUnderAttack("player"),
        sc.UnitHPLessThan("player", 60),
    }
}

Triggers.Brew.DiffuseMagic = {
    Name="Diffuse Magic",
    Binding=scripty.Bindings.DiffuseMagic,
    Conditions={
        sc.AbilityUsable("Diffuse Magic"),
        sc.PlayerHasMagicDebuff(),
    }
}

Triggers.Brew.TigerPalm = {
    Name="Tiger Palm",
    Binding=scripty.Bindings.TigerPalm,
    Conditions={
        sc.AbilityUsable("Tiger Palm"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Brew.ChiWave = {
    Name="Chi Wave",
    Binding=scripty.Bindings.ChiWave,
    Conditions={
        sc.AbilityUsable("Chi Wave"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Brew.RisingSunKick = {
    Name="Rising Sun Kick",
    Binding=scripty.Bindings.RisingSunKick,
    Conditions={
        sc.AbilityUsable("Rising Sun Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.Brew.RushingJadeWind = {
    Name="Rushing Jade Wind",
    Binding=scripty.Bindings.RushingJadeWind,
    Conditions={
        sc.AbilityUsable("Rushing Jade Wind"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
        Inverse(sc.UnitHasAura("player", "Rushing Jade Wind", "HELPFUL")),
    }
}

Triggers.Brew.RushingJadeWindLow = {
    Name="Rushing Jade Wind",
    Binding=scripty.Bindings.RushingJadeWind,
    Conditions={
        sc.AbilityUsable("Rushing Jade Wind"),
    }
}

Triggers.Brew.Paralysis = {
    Name="Paralysis",
    Binding=scripty.Bindings.Paralysis,
    Conditions={
        sc.AbilityUsable("Paralysis"),
        sc.UnitAttackable("target"),
        sc.TargetIsReadyForInterrupt(),
        sc.EnemyUnitWithin("target", 20),
    }
}

Triggers.Brew.ParalysisPvP = {
    Name="Paralysis",
    Binding=scripty.Bindings.Paralysis,
    Conditions={
        sc.AbilityUsable("Paralysis"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
        sc.UnitIsPlayer("target"),
    }
}

Triggers.Brew.BreathOfFire = {
    Name="Breath of Fire",
    Binding=scripty.Bindings.BreathOfFire,
    Conditions={
        sc.AbilityUsable("Breath of Fire"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
        Inverse(sc.UnitHasAura("player", "Blackout Combo", "HELPFUL")),
    }
}

Triggers.Brew.BreathOfFirePvP = {
    Name="Breath of Fire",
    Binding=scripty.Bindings.BreathOfFire,
    Conditions={
        sc.AbilityUsable("Breath of Fire"),
        sc.UnitIsPlayer("target"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Blackout Combo", "HELPFUL")),
    }
}

Triggers.Brew.VivifyMaintenance = {
    Name="Vivify Me",
    Focus="player",
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHasAura("player", "Vivacious Vivification", "HELPFUL"),
        sc.UnitHPLessThan("player", 85),
    }
}

Triggers.Brew.Transcendence = {
    Name="Transcendence",
    Binding=scripty.Bindings.Transcendence,
    Conditions={
        sc.AbilityUsable("Transcendence"),
        Inverse(sc.AbilityUsable("Transcendence: Transfer")),
    }
}

Triggers.Brew.TranscendenceSaveMe = {
    Name="Transcendence",
    Binding=scripty.Bindings.Transcendence,
    Conditions={
        sc.AbilityUsable("Transcendence"),
        Inverse(sc.AbilityUsable("Transcendence: Transfer")),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.Brew.TranscendenceTransferSaveMe = {
    Name="Transcendence",
    Binding=scripty.Bindings.TranscendenceTransfer,
    Conditions={
        sc.AbilityUsable("Transcendence: Transfer"),
        Inverse(sc.AbilityWasCastRecently("Transcendence: Transfer", 10)),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.Brew.VivifyAnyone = {
    Name="Vivify on the run",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Brew.VivifyLow = {
    Name="Vivify others",
    Focus="player",
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.Brew.ExplodingKeg = {
    Name="Exploding Keg",
    Binding=scripty.Bindings.ExplodingKeg,
    Conditions={
        sc.AbilityUsable("Exploding Keg"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(2),
    }
}

Triggers.Brew.ExplodingKegSingle = {
    Name="Exploding Keg",
    Binding=scripty.Bindings.ExplodingKeg,
    Conditions={
        sc.AbilityUsable("Exploding Keg"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
    }
}

--WW
Triggers.WW.ExpelHarm = {
    Name="Expel Harm",
    Binding=scripty.Bindings.ExpelHarm,
    Conditions={
        sc.AbilityUsable("Expel Harm"),
        sc.KeepCombo("Expel Harm"),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_CHI, 5))
    }
}

Triggers.WW.LegSweep = {
    Name="Leg Sweep",
    Binding=scripty.Bindings.LegSweep,
    Conditions={
        sc.AbilityUsable("Leg Sweep"),
        sc.AbilityInRange("Blackout Kick", "target"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
    }
}

Triggers.WW.DampenHarm = {
    Name="Dampen Harm",
    Binding=scripty.Bindings.DampenHarm,
    Conditions={
        sc.AbilityUsable("Dampen Harm"),
        sc.UnitUnderAttack("player"),
        sc.UnitHPLessThan("player", 60),
        Inverse(sc.UnitHasAura("player", "Vivacious Vivification", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Touch of Karma", "HELPFUL")),
        Inverse(sc.AbilityCDLessThan("Touch of Karma", 3)),
    }
}

Triggers.WW.DiffuseMagic = {
    Name="Diffuse Magic",
    Binding=scripty.Bindings.DiffuseMagic,
    Conditions={
        sc.AbilityUsable("Diffuse Magic"),
        Inverse(sc.UnitHasAura("player", "Touch of Karma", "HELPFUL")),
        sc.PlayerUnderMagicAssault(),
    }
}

Triggers.WW.SpinningCraneKickProc = {
    Name="Spinning Crane Kick Proc",
    Binding=scripty.Bindings.SpinningCraneKick,
    Conditions={
        sc.AbilityUsable("Spinning Crane Kick"),
        sc.UnitAttackable("target"),
        sc.KeepCombo("Spinning Crane Kick"),
        sc.UnitHasAura("player", "Dance of Chi-Ji", "HELPFUL"),
        sc.NumberOfMeleeEnemiesGreaterThan(0)
    }
}

Triggers.WW.SpinningCraneKick = {
    Name="Spinning Crane Kick",
    Binding=scripty.Bindings.SpinningCraneKick,
    Conditions={
        sc.AbilityUsable("Spinning Crane Kick"),
        sc.UnitAttackable("target"),
        sc.KeepCombo("Spinning Crane Kick"),
        sc.NumberOfMeleeEnemiesGreaterThan(1),
    }
}

Triggers.WW.FistsOfFury = {
    Name="Fists of Fury",
    Binding=scripty.Bindings.FistsOfFury,
    Conditions={
        sc.AbilityUsable("Fists of Fury"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.WW.WhiteTiger = {
    Name="White Tiger",
    Binding=scripty.Bindings.WhiteTiger,
    Conditions={
        sc.AbilityUsable("Summon White Tiger Statue"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.WW.Xuen = {
    Name="Invoke Xuen, the White Tiger",
    Binding=scripty.Bindings.Xuen,
    Conditions={
        sc.AbilityUsable("Invoke Xuen, the White Tiger"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
    }
}

Triggers.WW.TouchOfKarma = {
    Name="Touch of Karma",
    Binding=scripty.Bindings.TouchOfKarma,
    Conditions={
        sc.AbilityUsable("Touch of Karma"),
        sc.UnitAttackable("target"),
        -- sc.UnitUnderAttack("player"),
        sc.EnemyUnitWithin("target", 20),
    }
}

Triggers.WW.WeaponsOfOrder = {
    Name="Weapons of Order",
    Binding=scripty.Bindings.WeaponsOfOrder,
    Conditions={
        sc.AbilityUsable("Weapons of Order"),
        sc.UnitAttackable("target"),
    }
}

Triggers.WW.TouchOfDeath = {
    Name="Touch of Death",
    Binding=scripty.Bindings.TouchOfDeath,
    Conditions={
        sc.AbilityUsable("Touch of Death"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.WW.SpearHandStrike = {
    Name="Spear Hand Strike",
    Binding=scripty.Bindings.SpearHandStrike,
    Conditions={
        sc.AbilityUsable("Spear Hand Strike"),
        sc.EnemyUnitInMelee("target"),
        sc.TargetIsReadyForInterrupt(),
        Inverse(sc.MistHealingActive()),
    }
}

Triggers.WW.RushingJadeWind = {
    Name="Rushing Jade Wind",
    Binding=scripty.Bindings.RushingJadeWind,
    Conditions={
        sc.AbilityUsable("Rushing Jade Wind"),
        sc.UnitAttackable("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
    }
}

Triggers.WW.BlackoutKick = {
    Name="Blackout Kick",
    Binding=scripty.Bindings.BlackoutKick,
    Conditions={
        sc.AbilityUsable("Blackout Kick"),
        sc.KeepCombo("Blackout Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.WW.TigerPalm = {
    Name="Tiger Palm",
    Binding=scripty.Bindings.TigerPalm,
    Conditions={
        sc.AbilityUsable("Tiger Palm"),
        sc.KeepCombo("Tiger Palm"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 10),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_CHI, 5))
    }
}

Triggers.WW.ChiWave = {
    Name="Chi Wave",
    Binding=scripty.Bindings.ChiWave,
    Conditions={
        sc.AbilityUsable("Chi Wave"),
        sc.KeepCombo("Chi Wave"),
        sc.UnitAttackable("target"),
        Inverse(sc.MistHealingActive()),
    }
}


Triggers.WW.StormEarthandFire = {
    Name="StormEarthandFire",
    Binding=scripty.Bindings.StormEarthandFire,
    Conditions={
        sc.AbilityUsable("Storm, Earth, and Fire"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.SpellChargesGreaterThan("Storm, Earth, and Fire", 1),
        Inverse(sc.UnitHasAura("player", "Storm, Earth, and Fire", "HELPFUL")),
    }
}

Triggers.WW.StormEarthandFire2 = {
    Name="StormEarthandFire",
    Binding=scripty.Bindings.StormEarthandFire,
    Conditions={
        sc.AbilityUsable("Storm, Earth, and Fire"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.NumberOfMeleeEnemiesGreaterThan(1),
        Inverse(sc.UnitHasAura("player", "Storm, Earth, and Fire", "HELPFUL")),
    }
}

Triggers.WW.Transcendence = {
    Name="Transcendence",
    Binding=scripty.Bindings.Transcendence,
    Conditions={
        sc.AbilityUsable("Transcendence"),
        sc.UnitHPLessThan("player", 65), 
        Inverse(sc.UnitHasAura("player", "Transcendence", "HELPFUL")),
        Inverse(sc.AbilityUsable("Transcendence: Transfer")),
    }
}

Triggers.WW.TranscendenceTransfer = {
    Name="Transcendence: Transfer",
    Binding=scripty.Bindings.TranscendenceTransfer,
    Conditions={
        sc.AbilityUsable("Transcendence: Transfer"),
        Inverse(sc.UnitHasAura("player", "Escape from Reality", "HELPFUL")),
        sc.UnitHPLessThan("player", 65), 
    }
}

Triggers.WW.TigersLust = {
    Name="Tigers Lust",
    Binding=scripty.Bindings.TigersLust,
    Conditions={
        sc.AbilityUsable("Tiger's Lust"),
        sc.PlayerBeenMoving(),
        sc.UnitSpeedLessThan("player", 7.5), 
    }
}

Triggers.WW.StrikeOfTheWindlord = {
    Name="Strike of the Windlord",
    Binding=scripty.Bindings.StrikeOfTheWindlord,
    Conditions={
        sc.AbilityUsable("Strike of the Windlord"),
        sc.KeepCombo("Strike of the Windlord"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.WW.RisingSunKick = {
    Name="Rising Sun Kick",
    Binding=scripty.Bindings.RisingSunKick,
    Conditions={
        sc.AbilityUsable("Rising Sun Kick"),
        sc.KeepCombo("Rising Sun Kick"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.WW.VivifyMe = {
    Name="Vivify Me",
    Focus="player",
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHasAura("player", "Vivacious Vivification", "HELPFUL"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.WW.VivifyRun = {
    Name="Vivify on the run",
    Focus="player",
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHPLessThan("player", 90),
    }
}

Triggers.WW.VivifyLow = {
    Name="Vivify others",
    Focus="player",
    Binding=scripty.Bindings.Vivify,
    Conditions={
        sc.AbilityUsable("Vivify"),
        sc.UnitHasAura("player", "Vivacious Vivification", "HELPFUL"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

--BM
Triggers.BM.SingleSpec = {
    Name="Single Spec",
    Binding=scripty.Bindings.HunterSetSingle,
    Conditions={
        sc.SpecAndSetShouldBe("single"),
        Inverse(sc.IsUnitInCombat("player")),
        Inverse(sc.SpecMismatch()),
    }
}

Triggers.BM.AoeSpec = {
    Name="Aoe Spec",
    Binding=scripty.Bindings.HunterSetAoe,
    Conditions={
        sc.SpecAndSetShouldBe("aoe"),
        Inverse(sc.IsUnitInCombat("player")),
        Inverse(sc.SpecMismatch()),
    }
}

Triggers.BM.PvpSpec = {
    Name="Pvp Spec",
    Binding=scripty.Bindings.HunterSetPvp,
    Conditions={
        sc.SpecAndSetShouldBe("pvp"),
        Inverse(sc.IsUnitInCombat("player")),
        Inverse(sc.SpecMismatch()),
    }
}

Triggers.BM.CallOfTheWild = {
    Name="Call of the Wild",
    Binding=scripty.Bindings.CallOfTheWild,
    Conditions={
        sc.AbilityUsable("Call of the Wild"),
        sc.UnitAttackable("target"),
    }
}
Triggers.BM.HuntersMark = {
    Name="Hunters Mark Shot",
    Binding=scripty.Bindings.HuntersMark,
    Conditions={
        sc.AbilityUsable("Hunter's Mark"),
        Inverse(sc.AbilityWasCastRecently("Hunter's Mark", 15)),
        sc.UnitIsUnit("target", "boss1"),
        Inverse(sc.UnitHPLessThan("target", 85)),
        Inverse(sc.UnitHasAura("target", "Hunter's Mark", "HELPFUL")),
    }
}

Triggers.BM.TranquilizingShot = {
    Name="Tranquilizing Shot",
    Binding=scripty.Bindings.TranquilizingShot,
    Conditions={
        sc.AbilityUsable("Tranquilizing Shot"),
        sc.EnemyTargetNeedsSooth(),
    }
}

Triggers.BM.ScareBeastGhosts = {
    Name="Scare Beast",
    Binding=scripty.Bindings.ScareBeast,
    Conditions={
        sc.AbilityUsable("Scare Beast"),
        sc.UnitNameIs("target", "Incorporeal Being"),
    }
}

Triggers.BM.FrostTrap = {
    Name="Frost Trap",
    Binding=scripty.Bindings.FrostTrap,
    Conditions={
        function() return false end,
    }
}
Triggers.BM.SummonPetOne = {
    Name="Summon Pet",
    Binding=scripty.Bindings.SummonPetOne,
    Conditions={
        sc.AbilityUsable("Call Pet 1"),
        sc.MissingPet(),
    }
}
Triggers.BM.MendPet = {
    Name="Mend Pet",
    Binding=scripty.Bindings.MendPet,
    Conditions={
        sc.AbilityUsable("Mend Pet"),
        sc.HurtPet(),
    }
}
Triggers.BM.RevivePet = {
    Name="Revive Pet",
    Binding=scripty.Bindings.RevivePet,
    Conditions={
        sc.AbilityUsable("Revive Pet"),
        Inverse(sc.LivePet()),
    }
}
Triggers.BM.SummonPetOne = {
    Name="Summon Pet",
    Binding=scripty.Bindings.SummonPetOne,
    Conditions={
        sc.AbilityUsable("Call Pet 3"),
        sc.MissingPet(),
        Inverse(sc.AbilityWasCastRecently("Call Pet 3", 5)),
    }
}
Triggers.BM.BindingShot = {
    Name="Binding Shot",
    Binding=scripty.Bindings.BindingShot,
    Conditions={
        function() return false end,
    }
}

Triggers.BM.ImplosiveTrap = {
    Name="Implosive Trap",
    Binding=scripty.Bindings.ImplosiveTrap,
    Conditions={
        function() return false end,
    }
}

Triggers.BM.Misdirection = {
    Name="Misdirection",
    Focus={name="MDTarget"},
    Binding=scripty.Bindings.Misdirection,
    Conditions={
        sc.AbilityUsable("Misdirection"),
        sc.UnitAttackable("target"),
        --sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.ScatterShot = {
    Name="Scatter Shot",
    Binding=scripty.Bindings.ScatterShot,
    Conditions={
        sc.AbilityUsable("Scatter Shot"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
        sc.AbilityInRange("Scatter Shot", "target"),
    }
}

Triggers.BM.Intimidation = {
    Name="Intimidation",
    Binding=scripty.Bindings.Intimidation,
    Conditions={
        sc.AbilityUsable("Intimidation"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
    }
}

Triggers.BM.DeathChakram = {
    Name="Death Chakram",
    Binding=scripty.Bindings.DeathChakram,
    Conditions={
        sc.AbilityUsable("Death Chakram"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.KillCommand = {
    Name="Kill Command",
    Binding=scripty.Bindings.KillCommand,
    Conditions={
        sc.AbilityUsable("Kill Command"),
        sc.UnitExists("pettarget"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.DireBeast = {
    Name="Dire Beast",
    Binding=scripty.Bindings.DireBeast,
    Conditions={
        sc.AbilityUsable("Dire Beast"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.Bloodshed = {
    Name="Bloodshed",
    Binding=scripty.Bindings.Bloodshed,
    Conditions={
        sc.AbilityUsable("Bloodshed"),
        sc.UnitExists("pettarget"),
        sc.UnitAttackable("target"),
    }
}
Triggers.BM.BarbedShotPrimary = {
    Name="Barbed Shot",
    Binding=scripty.Bindings.BarbedShot,
    Conditions={
        sc.AbilityUsable("Barbed Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
        sc.UnitAuraDurationLessThan("pet", "Frenzy", 1.5),
    }
}
Triggers.BM.BarbedShot = {
    Name="Barbed Shot",
    Binding=scripty.Bindings.BarbedShot,
    Conditions={
        sc.AbilityUsable("Barbed Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.BarbedShot2 = {
    Name="Barbed Shot",
    Binding=scripty.Bindings.BarbedShot,
    Conditions={
        sc.AbilityUsable("Barbed Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.BestialWrath = {
    Name="Bestial Wrath",
    Binding=scripty.Bindings.BestialWrath,
    Conditions={
        sc.AbilityUsable("Bestial Wrath"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.KillShotPrimary = {
    Name="Kill Shot",
    Binding=scripty.Bindings.KillShot,
    Conditions={
        sc.AbilityUsable("Kill Shot"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Serpent Sting", 6),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.KillShot = {
    Name="Kill Shot",
    Binding=scripty.Bindings.KillShot,
    Conditions={
        sc.AbilityUsable("Kill Shot"),
        sc.UnitAttackable("target"),
        -- sc.UnitHPLessThan("target", 20),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}
Triggers.BM.ChimaeralSting = {
    Name="Chimaeral Sting",
    Binding=scripty.Bindings.ChimaeralSting,
    Conditions={
        sc.AbilityUsable("Chimaeral Sting"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Barbed Shot", "target"),

    }
}
Triggers.BM.CobraShot = {
    Name="Cobra Shot",
    Binding=scripty.Bindings.CobraShot,
    Conditions={
        sc.AbilityUsable("Cobra Shot"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_FOCUS, 55),
        sc.AbilityInRange("Barbed Shot", "target"),

    }
}
Triggers.BM.MultiShot = {
    Name="Multi-Shot",
    Binding=scripty.Bindings.MultiShot,
    Conditions={
        sc.AbilityUsable("Multi-Shot"),
        sc.UnitAttackable("target"),
        sc.NumberOfEnemiesGreaterThan(1),
        sc.UnitAuraDurationLessThan("pet", "Beast Cleave", 1.5, "HELPFUL"),
        sc.AbilityInRange("Barbed Shot", "target"),
    }
}

--Marks
Triggers.Marks.SniperShot = {
    Name="Sniper Shot",
    Binding=scripty.Bindings.SniperShot,
    Conditions={
        sc.AbilityUsable("Sniper Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Sniper Shot", "target"),
    }
}
Triggers.Marks.AimedShot = {
    Name="Aimed Shot",
    Binding=scripty.Bindings.AimedShot,
    Conditions={
        sc.AbilityUsable("Aimed Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.AimedShotTrueshot = {
    Name="Aimed Shot",
    Binding=scripty.Bindings.AimedShot,
    Conditions={
        sc.AbilityUsable("Aimed Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
        sc.UnitHasAura("player", "Trueshot", "HELPFUL"),
    }
}

Triggers.Marks.BurstingShot = {
    Name="Bursting Shot",
    Binding=scripty.Bindings.BurstingShot,
    Conditions={
        sc.AbilityUsable("Bursting Shot"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 10),
    }
}

Triggers.Marks.RapidFire = {
    Name="Rapid Fire",
    Binding=scripty.Bindings.RapidFire,
    Conditions={
        sc.AbilityUsable("Rapid Fire"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.Trueshot = {
    Name="Trueshot",
    Binding=scripty.Bindings.Trueshot,
    Conditions={
        sc.AbilityUsable("Trueshot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.MendPet = {
    Name="Mend Pet",
    Binding=scripty.Bindings.MendPet,
    Conditions={
        sc.AbilityUsable("Mend Pet"),
        sc.UnitHPLessThan("pet", 65),
    }
}

Triggers.Marks.WailingArrow = {
    Name="Wailing Arrow",
    Binding=scripty.Bindings.WailingArrow,
    Conditions={
        sc.AbilityUsable("Wailing Arrow"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.ExplosiveShot = {
    Name="Explosive Shot",
    Binding=scripty.Bindings.ExplosiveShot,
    Conditions={
        sc.AbilityUsable("Explosive Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.ArcaneShot = {
    Name="Arcane Shot",
    Binding=scripty.Bindings.ArcaneShot,
    Conditions={
        sc.AbilityUsable("Arcane Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
        sc.UnitHasAura("player", "Precise Shots", "HELPFUL"),
    }
}

Triggers.Marks.AspectoftheTurtle = {
    Name="Aspect of the Turtle",
    Binding=scripty.Bindings.AspectoftheTurtle,
    Conditions={
        sc.AbilityUsable("Aspect of the Turtle"),
        sc.UnitHPLessThan("player", 35),
        Inverse(sc.AbilityCDLessThan("Exhilaration", 3)),
    }
}

Triggers.Marks.Camouflage = {
    Name="Camouflage",
    Binding=scripty.Bindings.Camouflage,
    Conditions={
        sc.AbilityUsable("Camouflage"),
    }
}

Triggers.BM.CounterShot = {
    Name="Counter Shot",
    Binding=scripty.Bindings.CounterShot,
    Conditions={
        sc.AbilityUsable("Counter Shot"),
        sc.TargetIsReadyForInterrupt(),
        sc.AbilityInRange("Barbed Shot", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Marks.CounterShot = {
    Name="Counter Shot",
    Binding=scripty.Bindings.CounterShot,
    Conditions={
        sc.AbilityUsable("Counter Shot"),
        sc.TargetIsReadyForInterrupt(),
        sc.AbilityInRange("Aimed Shot", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Marks.Exhilaration = {
    Name="Exhilaration",
    Binding=scripty.Bindings.Exhilaration,
    Conditions={
        sc.AbilityUsable("Exhilaration"),
        sc.UnitHPLessThan("player", 50),
    }
}

Triggers.Marks.TarTrap = {
    Name="Tar Trap",
    Binding=scripty.Bindings.TarTrap,
    Conditions={
        function() return false end,
    }
}

Triggers.Marks.FreezingTrap = {
    Name="Freezing Trap",
    Binding=scripty.Bindings.FreezingTrap,
    Conditions={
        sc.AbilityUsable("Freezing Trap"),
        Inverse(sc.UnitAttackable("target")),
    }
}

Triggers.Marks.HighExplosiveTrap = {
    Name="High Explosive Trap",
    Binding=scripty.Bindings.HighExplosiveTrap,
    Conditions={
        sc.AbilityUsable("High Explosive Trap"),
        Inverse(sc.UnitAttackable("target")),
    }
}

Triggers.Marks.KillShot = {
    Name="Kill Shot",
    Binding=scripty.Bindings.KillShot,
    Conditions={
        sc.AbilityUsable("Kill Shot"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("target", 20),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.KillShotProc = {
    Name="Kill Shot",
    Binding=scripty.Bindings.KillShot,
    Conditions={
        sc.AbilityUsable("Kill Shot"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Razor Fragments", "HELPFUL"),
        sc.AbilityInRange("Aimed Shot", "target"),
        Inverse(sc.UnitHasAura("player", "Trueshot", "HELPFUL")),
    }
}

Triggers.Marks.SteadyShot = {
    Name="Steady Shot",
    Binding=scripty.Bindings.SteadyShot,
    Conditions={
        sc.AbilityUsable("Steady Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
    }
}

Triggers.Marks.SteadyShotForBuff = {
    Name="Steady Shot",
    Binding=scripty.Bindings.SteadyShot,
    Conditions={
        sc.AbilityUsable("Steady Shot"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Aimed Shot", "target"),
        sc.UnitAuraDurationLessThan("player", "Steady Focus", 2, "HELPFUL"),
        Inverse(sc.UnitHasAura("player", "Trueshot", "HELPFUL")),
    }
}

--Fire
Triggers.Fire.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellFire(),
    }
}

Triggers.Fire.FleshcraftLow = {
    Name="Fleshcraft",
    Binding=scripty.Bindings.Fleshcraft,
    Conditions={
        sc.AbilityUsable("Fleshcraft"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.Fire.IceFloes = {
    Name="Ice Floes",
    Binding=scripty.Bindings.IceFloes,
    Conditions={
        sc.AbilityUsable("Ice Floes"),
        Inverse(sc.UnitHasAura("player", "Ice Floes", "HELPFUL")),
        sc.PlayerIsCastingAbility("Pyroblast"),
    }
}

Triggers.Fire.Frostbolt = {
    Name="Frostbolt",
    Binding=scripty.Bindings.Frostbolt,
    Conditions={
        sc.AbilityUsable("Frostbolt"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Fire.Spellsteal = {
    Name="Spellsteal",
    Binding=scripty.Bindings.Spellsteal,
    Conditions={
        sc.AbilityUsable("Spellsteal"),
        Inverse(sc.AbilityWasCastRecently("Spellsteal", 6)),
        sc.UnitHasStealable("target"),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 60),
    }
}

Triggers.Fire.FireBlastCritConfirm = {
    Name="Fire Blast",
    Binding=scripty.Bindings.FireBlast,
    Conditions={
        sc.AbilityUsable("Fire Blast"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Heating Up", "HELPFUL"),
    }
}

Triggers.Fire.FireBlastAvoidCap = {
    Name="Fire Blast",
    Binding=scripty.Bindings.FireBlast,
    Conditions={
        sc.AbilityUsable("Fire Blast"),
        sc.UnitAttackable("target"),
        sc.SpellChargesGreaterThan("Fire Blast", 2),
    }
}

Triggers.Fire.Deathborne = {
    Name="Deathborne",
    Binding=scripty.Bindings.Deathborne,
    Conditions={
        sc.AbilityUsable("Deathborne"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
    }
}

Triggers.Fire.BlazingBarrier = {
    Name="Blazing Barrier",
    Binding=scripty.Bindings.BlazingBarrier,
    Conditions={
        sc.AbilityUsable("Blazing Barrier"),
        Inverse(sc.UnitHasAura("player", "Blazing Barrier", "HELPFUL")),
    }
}

Triggers.Fire.BlazingBarrier2 = {
    Name="Blazing Barrier",
    Binding=scripty.Bindings.BlazingBarrier,
    Conditions={
        sc.AbilityUsable("Blazing Barrier"),
        sc.UnitHPLessThan("player", 75),
    }
}

Triggers.Fire.Combustion = {
    Name="Combustion",
    Binding=scripty.Bindings.Combustion,
    Conditions={
        sc.AbilityUsable("Combustion"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
    }
}

Triggers.Fire.Fireball = {
    Name="Fireball",
    Binding=scripty.Bindings.Fireball,
    Conditions={
        sc.AbilityUsable("Fireball"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Fire.LivingBomb = {
    Name="Living Bomb",
    Binding=scripty.Bindings.LivingBomb,
    Conditions={
        sc.AbilityUsable("Living Bomb"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
    }
}

Triggers.Fire.PhoenixFlames = {
    Name="Phoenix Flames",
    Binding=scripty.Bindings.PhoenixFlames,
    Conditions={
        sc.AbilityUsable("Phoenix Flames"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
    }
}

Triggers.Fire.Pyroblast = {
    Name="Pyroblast",
    Binding=scripty.Bindings.Pyroblast,
    Conditions={
        sc.AbilityUsable("Pyroblast"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Fire.IceNova = {
    Name="Ice Nova",
    Binding=scripty.Bindings.IceNova,
    Conditions={
        sc.AbilityUsable("Ice Nova"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Fire.PyroblastProc = {
    Name="Pyroblast",
    Binding=scripty.Bindings.Pyroblast,
    Conditions={
        sc.AbilityUsable("Pyroblast"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Hot Streak!", "HELPFUL"),
    }
}

Triggers.Fire.Scorch = {
    Name="Scorch",
    Binding=scripty.Bindings.Scorch,
    Conditions={
        sc.AbilityUsable("Scorch"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Fire.ScorchExecute = {
    Name="Scorch",
    Binding=scripty.Bindings.Scorch,
    Conditions={
        sc.AbilityUsable("Scorch"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("target", 30),
    }
}

Triggers.Fire.ShiftingPower = {
    Name="Shifting Power",
    Binding=scripty.Bindings.ShiftingPower,
    Conditions={
        sc.AbilityUsable("Shifting Power"),
        Inverse(sc.AbilityCDLessThan("Combustion", 15)),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
    }
}

--Arcane
Triggers.Arcane.TemporalShield = {
    Name="Temporal Shield",
    Binding=scripty.Bindings.TemporalShield,
    Conditions={
        sc.AbilityUsable("Temporal Shield"),
        sc.UnitHPDamageAtLeastTakenSince("player", 5, 3),
    }
}

Triggers.Arcane.DragonsBreath = {
    Name="Dragon's Breath",
    Binding=scripty.Bindings.DragonsBreath,
    Conditions={
        sc.AbilityUsable("Dragon's Breath"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 15),
        sc.TargetWantsStun(),
    }
}

Triggers.Arcane.Prepot = {
    Name="Prepot",
    Binding=scripty.Bindings.Prepot,
    Conditions={
        sc.UnitAttackable("target"),
        sc.ItemIsReady(SOPHIE_ITEM_PREPOTION_INTELLECT),
        sc.AvoidingPenance(),
        sc.UnitHasAura("player", "Arcane Surge", "HELPFUL"),
    }
}
Triggers.Arcane.UseTrinket = {
    Name="Trinket",
    Binding=scripty.Bindings.Trinket,
    Conditions={
        sc.TrinketIsReady(1),
        sc.UnitAttackable("target"),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
        sc.UnitHasAura("player", "Arcane Surge", "HELPFUL"),
    }
}
Triggers.Arcane.UseTrinket2 = {
    Name="Trinket2",
    Binding=scripty.Bindings.Trinket2,
    Conditions={
        sc.TrinketIsReady(2),
        sc.UnitAttackable("target"),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
        sc.UnitHasAura("player", "Arcane Surge", "HELPFUL"),
    }
}
Triggers.Arcane.AlterTime = {
    Name="Alter Time",
    Binding=scripty.Bindings.AlterTime,
    Conditions= {
        function() return false end,
    }
}

Triggers.Arcane.GreaterInvisibility = {
    Name="Greater Invisibility",
    Binding=scripty.Bindings.GreaterInvisibility,
    Conditions= {
        function() return false end,
    }
}

Triggers.Arcane.Blink = {
    Name="Blink",
    Binding=scripty.Bindings.Blink,
    Conditions= {
        function() return false end,
    }
}

Triggers.Arcane.IceBlock = {
    Name="Ice Block",
    Binding=scripty.Bindings.IceBlock,
    Conditions= {
        function() return false end,
    }
}

Triggers.Arcane.TouchOfTheMagi = {
    Name="Touch of the Magi",
    Binding=scripty.Bindings.TouchOfTheMagi,
    Conditions={
        sc.AbilityUsable("Touch of the Magi"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.SuperNova = {
    Name="Supernova",
    Binding=scripty.Bindings.SuperNova,
    Conditions={
        sc.AbilityUsable("Supernova"),
        sc.UnitAttackable("target"),
        sc.UnitIsUnit("targettarget", "player"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.SheepGhosts = {
    Name="Polymorph",
    Binding=scripty.Bindings.Polymorph,
    Conditions={
        sc.AbilityUsable("Polymorph"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.PrismaticBarrier = {
    Name="Prismatic Barrier",
    Binding=scripty.Bindings.PrismaticBarrier,
    Conditions={
        sc.AbilityUsable("Prismatic Barrier"),
        
    }
}

Triggers.Arcane.MassBarrier = {
    Name="Mass Barrier",
    Binding=scripty.Bindings.MassBarrier,
    Conditions={
        sc.AbilityUsable("Mass Barrier"),
        Inverse(sc.UnitHasAura("player", "Prismatic Barrier", "HELPFUL")),
    }
}

Triggers.Arcane.PrismaticBarrier2 = {
    Name="Prismatic Barrier",
    Binding=scripty.Bindings.PrismaticBarrier,
    Conditions={
        sc.AbilityUsable("Prismatic Barrier"),
        Inverse(sc.UnitHasAura("player", "Prismatic Barrier", "HELPFUL")),
    }
}

Triggers.Arcane.PresenceOfMind = {
    Name="Presence of Mind",
    Binding=scripty.Bindings.PresenceOfMind,
    Conditions={
        sc.AbilityUsable("Presence of Mind"),
    }
}

Triggers.Arcane.ArcaneSurge = {
    Name="Arcane Surge",
    Binding=scripty.Bindings.ArcaneSurge,
    Conditions={
        sc.AbilityUsable("Arcane Surge"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ArcaneOrb = {
    Name="Arcane Orb",
    Binding=scripty.Bindings.ArcaneOrb,
    Conditions={
        sc.AbilityUsable("Arcane Orb"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),    
        Inverse(sc.DangerousToCast()),
    }
}

Triggers.Arcane.ArcaneMissiles = {
    Name="Arcane Missiles",
    Binding=scripty.Bindings.ArcaneMissiles,
    Conditions={
        sc.AbilityUsable("Arcane Missiles"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ArcaneMissilesBattery = {
    Name="Arcane Missiles",
    Binding=scripty.Bindings.ArcaneMissiles,
    Conditions={
        sc.AbilityUsable("Arcane Missiles"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Arcane Battery", "HELPFUL"),
        sc.UnitHasAura("player", "Clearcasting", "HELPFUL"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ArcaneMissilesClearCast = {
    Name="Arcane Missiles",
    Binding=scripty.Bindings.ArcaneMissiles,
    Conditions={
        sc.AbilityUsable("Arcane Missiles"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Clearcasting", "HELPFUL"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ArcaneMissilesClearCastCap = {
    Name="Arcane Missiles",
    Binding=scripty.Bindings.ArcaneMissiles,
    Conditions={
        sc.AbilityUsable("Arcane Missiles"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Clearcasting", 3, "HELPFUL")),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ArcaneBlastPrecision = {
    Name="Arcane Blast",
    Binding=scripty.Bindings.ArcaneBlast,
    Conditions={
        sc.AbilityUsable("Arcane Blast"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Nether Precision", 2, "HELPFUL")),
    }
}

Triggers.Arcane.ArcaneBlastPrecision2 = {
    Name="Arcane Blast",
    Binding=scripty.Bindings.ArcaneBlast,
    Conditions={
        sc.AbilityUsable("Arcane Blast"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
        sc.UnitHasAura("player", "Nether Precision", "HELPFUL"),
        Inverse(sc.PlayerIsCastingAbility("Arcane Blast")),
    }
}

Triggers.Arcane.ArcaneBlast = {
    Name="Arcane Blast",
    Binding=scripty.Bindings.ArcaneBlast,
    Conditions={
        sc.AbilityUsable("Arcane Blast"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ArcaneBarrageMana = {
    Name="Arcane Barrage",
    Binding=scripty.Bindings.ArcaneBarrage,
    Conditions={
        sc.AbilityUsable("Arcane Barrage"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_ARCANECHARGES, 4),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80)),
        sc.AbilityOnCD("Evocation"),
        sc.AbilityInRange("Arcane Blast", "target"),
        Inverse(sc.DangerousToCast()),
        Inverse(sc.UnitHasAura("player", "Arcane Surge", "HELPFUL")),
    }
}

Triggers.Arcane.ArcaneBarrageAoE = {
    Name="Arcane Barrage",
    Binding=scripty.Bindings.ArcaneBarrage,
    Conditions={
        sc.AbilityUsable("Arcane Barrage"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimumAdjusted(SOPHIE_POWER_ARCANECHARGES, 4),
        sc.NumberOfEnemiesGreaterThan(2),
        sc.AbilityInRange("Arcane Blast", "target"),
        Inverse(sc.DangerousToCast()),
    }
}

Triggers.Arcane.Evocation = {
    Name="Evocation",
    Binding=scripty.Bindings.Evocation,
    Conditions={
        sc.AbilityUsable("Evocation"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Arcane Surge", "HELPFUL")),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 35)),
        Inverse(sc.PlayerIsCastingAbility("Arcane Surge")),
        Inverse(sc.AbilityWasCastRecently("Arcane Surge", 10)),
    }
}

Triggers.Arcane.EvocationSurgePrep = {
    Name="Evocation",
    Binding=scripty.Bindings.Evocation,
    Conditions={
        sc.AbilityUsable("Evocation"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Arcane Surge", "HELPFUL")),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 65)),
        sc.AbilityCDLessThan("Arcane Surge", 10),
    }
}

Triggers.Arcane.Spellsteal = {
    Name="Spellsteal",
    Binding=scripty.Bindings.Spellsteal,
    Conditions={
        sc.AbilityUsable("Spellsteal"),
        Inverse(sc.AbilityWasCastRecently("Spellsteal", 6)),
        sc.UnitHasStealable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.RemoveCurse = {
    Name="Remove Curse",
    Focus={name="purifyTarget"},
    Binding=scripty.Bindings.RemoveCurse,
    Conditions={
        sc.AbilityUsable("Remove Curse"),
    }
}

Triggers.Arcane.MirrorImage = {
    Name="Mirror Image",
    Binding=scripty.Bindings.MirrorImage,
    Conditions={
        sc.AbilityUsable("Mirror Image"),
        Inverse(sc.UnitHasAura("player", "Combustion", "HELPFUL")),
    }
}

Triggers.Arcane.Slow = {
    Name="Slow",
    Binding=scripty.Bindings.Slow,
    Conditions={
        sc.AbilityUsable("Slow"),
        sc.UnitAttackable("target"),
        sc.EnemyIn35YardRange("target"),
        Inverse(sc.UnitHasAura("target", "Slow", "HARMFUL")),
        sc.UnitIsUnit("targettarget", "player"),
        Inverse(sc.UnitSpeedLessThan("target", 6)),
        Inverse(sc.AbilityWasCastRecently("Slow", 9)),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.ShiftingPower = {
    Name="Shifting Power",
    Binding=scripty.Bindings.ShiftingPower,
    Conditions={
        sc.AbilityUsable("Shifting Power"),
        sc.EnemyUnitWithin("target", 15),
        Inverse(sc.UnitHasAuraOrSpell("player", "Arcane Surge", "HELPFUL")),
        Inverse(sc.AbilityCDLessThan("Arcane Surge", 10)),
    }
}

Triggers.Arcane.Invisibility = {
    Name="Invisibility",
    Binding=scripty.Bindings.Invisibility,
    Conditions={
        sc.AbilityUsable("Invisibility"),
        sc.UnitHPDamageAtLeastTakenSince("player", 50, 10),
        sc.UnitHPLessThan("player", 75), 
    }
}

Triggers.Arcane.Polymorph = {
    Name="Polymorph",
    Binding=scripty.Bindings.Polymorph,
    Conditions={
        sc.AbilityUsable("Polymorph"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Polymorph", "HARMFUL")),
        Inverse(sc.UnitHPLessThan("target", 95)),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.PolymorphPvP = {
    Name="Polymorph",
    Binding=scripty.Bindings.Polymorph,
    Conditions={
        sc.AbilityUsable("Polymorph"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("target", "Polymorph", "HARMFUL")),
        Inverse(sc.UnitHPLessThan("target", 95)),
        Inverse(sc.UnitHPDamageAtLeastTakenSince("target", 1, 10)),
        sc.UnitIsPlayer("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.IceFloes = {
    Name="Ice Floes",
    Binding=scripty.Bindings.IceFloes,
    Conditions={
        sc.AbilityUsable("Ice Floes"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitHasAura("player", "Foresight", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Ice Floes", "HELPFUL")),
        sc.PlayerMoving(),
    }
}

Triggers.Arcane.Frostbolt = {
    Name="Frostbolt",
    Binding=scripty.Bindings.Frostbolt,
    Conditions={
        sc.AbilityUsable("Frostbolt"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.FireBlast = {
    Name="Fire Blast",
    Binding=scripty.Bindings.FireBlast,
    Conditions={
        sc.AbilityUsable("Fire Blast"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Arcane Blast", "target"),
    }
}

Triggers.Arcane.FrostNova = {
    Name="Frost Nova",
    Binding=scripty.Bindings.FrostNova,
    Conditions={
        sc.AbilityUsable("Frost Nova"),
        sc.NumberOf10YardEnemiesGreaterThan(0),
    }
}

Triggers.Arcane.Counterspell = {
    Name="Counterspell",
    Binding=scripty.Bindings.Counterspell,
    Conditions={
        sc.AbilityUsable("Counterspell"),
        sc.UnitAttackable("target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.Arcane.ConeOfCold = {
    Name="Cone of Cold",
    Binding=scripty.Bindings.ConeOfCold,
    Conditions={
        sc.AbilityUsable("Cone of Cold"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitWithin("target", 20),
        sc.UnitIsUnit("targettarget", "player"),
    }
}

Triggers.Arcane.BlastWave = {
    Name="Blast Wave",
    Binding=scripty.Bindings.BlastWave,
    Conditions={
        sc.AbilityUsable("Blast Wave"),
        Inverse(sc.AbilityWasCastRecently("Frost Nova", 3)),
        sc.PlayerWasRecentlyMeleed(),
        sc.UnitExists("target"),
    }
}

Triggers.Arcane.ArcaneIntellect = {
    Name="Arcane Intellect",
    Binding=scripty.Bindings.ArcaneIntellect,
    Conditions={
        sc.AbilityUsable("Arcane Intellect"),
        sc.ArcaneNeeded(),
    }
}

Triggers.Arcane.ArcaneExplosion = {
    Name="Arcane Explosion",
    Binding=scripty.Bindings.ArcaneExplosion,
    Conditions={
        sc.AbilityUsable("Arcane Explosion"),
        sc.NumberOf10YardEnemiesGreaterThan(2),
    }
}

Triggers.Arcane.ArcaneExplosionLowPriority = {
    Name="Arcane Explosion",
    Binding=scripty.Bindings.ArcaneExplosion,
    Conditions={
        sc.AbilityUsable("Arcane Explosion"),
        sc.NumberOf10YardEnemiesGreaterThan(0),
    }
}

--Prot

Triggers.Prot.StormBolt = {
    Name="Storm Bolt",
    Binding=scripty.Bindings.StormBolt,
    Conditions={
        sc.AbilityUsable("Storm Bolt"),
        sc.EnemyUnitWithin("target", 20),
        sc.UnitIsPlayer("target"),
        Inverse(sc.AbilityWasCastRecently("Shield Charge", 15)),
        Inverse(sc.AbilityWasCastRecently("Storm Bolt", 15)),
        Inverse(sc.AbilityWasCastRecently("Shockwave", 15)),
    }
}

Triggers.Prot.StormBolt2 = {
    Name="Storm Bolt",
    Binding=scripty.Bindings.StormBolt,
    Conditions={
        sc.AbilityUsable("Storm Bolt"),
        sc.EnemyUnitWithin("target", 20),
        sc.EnemyInMeleeNeedsStun(),
        Inverse(sc.AbilityWasCastRecently("Shield Charge", 15)),
        Inverse(sc.AbilityWasCastRecently("Storm Bolt", 15)),
        Inverse(sc.AbilityWasCastRecently("Shockwave", 15)),
        Inverse(sc.UnitExists("boss1")),
    }
}
Triggers.Prot.RavagerAoe = {
    Name="Ravager",
    Binding=scripty.Bindings.Ravager,
    Conditions={
        sc.AbilityUsable("Ravager"),
        sc.UnitAttackable("target"),
        sc.NumberOf10YardEnemiesGreaterThan(2),
    }
}

Triggers.Prot.Ravager = {
    Name="Ravager",
    Binding=scripty.Bindings.Ravager,
    Conditions={
        sc.AbilityUsable("Ravager"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Prot.BattleShout = {
    Name="Battle Shout",
    Binding=scripty.Bindings.BattleShout,
    Conditions={
        sc.AbilityUsable("Battle Shout"),
        sc.ShoutNeeded(),
    }
}

Triggers.Prot.Charge = {
    Name="Charge",
    Binding=scripty.Bindings.Charge,
    Conditions={
        sc.AbilityUsable("Charge"),
        sc.UserDemandsCharge(),
        Inverse(sc.AbilityCDLessThan("Shield Charge", 2)),
        Inverse(sc.AbilityWasCastRecently("Shield Charge", 1)),
        Inverse(sc.EnemyUnitInMelee("target")),
    }
}

Triggers.Prot.ShieldCharge = {
    Name="Shield Charge",
    Binding=scripty.Bindings.ShieldCharge,
    Conditions={
        sc.AbilityUsable("Shield Charge"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Prot.DefensiveStance = {
    Name="Defensive Stance",
    Binding=scripty.Bindings.DefensiveStance,
    Conditions={
        sc.AbilityUsable("Defensive Stance"),
        Inverse(sc.UnitHasAura("player", "Defensive Stance", "HELPFUL")),
    }
}

Triggers.Prot.Execute = {
    Name="Execute",
    Binding=scripty.Bindings.Execute,
    Conditions={
        sc.AbilityUsable("Execute"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Ignore Pain", "HELPFUL"),
    }
}

Triggers.Prot.ExecuteProc = {
    Name="Execute",
    Binding=scripty.Bindings.Execute,
    Conditions={
        sc.AbilityUsable("Execute"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Sudden Death", "HELPFUL"),
    }
}

Triggers.Prot.ImpendingVictory = {
    Name="Impending Victory",
    Binding=scripty.Bindings.ImpendingVictory,
    Conditions={
        sc.AbilityUsable("Impending Victory"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHPLessThan("player", 70),
    }
}

Triggers.Prot.Pummel = {
    Name="Pummel",
    Binding=scripty.Bindings.Pummel,
    Conditions={
        sc.AbilityUsable("Pummel"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.Prot.RallyingCry = {
    Name="Rallying Cry",
    Binding=scripty.Bindings.RallyingCry,
    Conditions={
        sc.AbilityUsable("Rallying Cry"),
        sc.UnitHPLessThan("player", 50),
        sc.UnitHPDamageAtLeastTakenSince("player", 20, 3),
    }
}

Triggers.Prot.ShieldBlock = {
    Name="Shield Block",
    Binding=scripty.Bindings.ShieldBlock,
    Conditions={
        sc.AbilityUsable("Shield Block"),
        sc.EnemyUnitInMelee("target"),
        sc.SpellChargesGreaterThan("Shield Block", 1),
        sc.UnitAuraDurationLessThan("player", "Shield Block", 3, "HELPFUL"),
    }
}

Triggers.Prot.ShieldBlock2 = {
    Name="Shield Block2",
    Binding=scripty.Bindings.ShieldBlock,
    Conditions={
        sc.AbilityUsable("Shield Block"),
        sc.UnitHPDamagePhysicalAtLeastTakenSince("player", 1, 3),
        sc.UnitAuraDurationLessThan("player", "Shield Block", 3, "HELPFUL"),
    }
}

Triggers.Prot.ShieldSlam = {
    Name="Shield Slam",
    Binding=scripty.Bindings.ShieldSlam,
    Conditions={
        sc.AbilityUsable("Shield Slam"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Prot.RevengeAoe = {
    Name="Revenge Aoe",
    Binding=scripty.Bindings.Revenge,
    Conditions={
        sc.AbilityUsable("Revenge"),
        sc.UnitAttackable("target"),
        sc.NumberOf10YardEnemiesGreaterThan(2),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RAGE, 50),
    }
}

Triggers.Prot.Revenge = {
    Name="Revenge",
    Binding=scripty.Bindings.Revenge,
    Conditions={
        sc.AbilityUsable("Revenge"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RAGE, 70),
    }
}

Triggers.Prot.RevengeLow = {
    Name="Revenge",
    Binding=scripty.Bindings.Revenge,
    Conditions={
        sc.AbilityUsable("Revenge"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RAGE, 80),
        sc.UnitHasAura("player", "Ignore Pain", "HELPFUL"),
    }
}

Triggers.Prot.RevengeProc = {
    Name="Revenge Proc",
    Binding=scripty.Bindings.Revenge,
    Conditions={
        sc.AbilityUsable("Revenge"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Revenge!", "HELPFUL"),
    }
}

Triggers.Prot.ThunderousRoar = {
    Name="Thunderous Roar",
    Binding=scripty.Bindings.ThunderousRoar,
    Conditions={
        sc.AbilityUsable("Thunderous Roar"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.Prot.ThunderousRoarAoe = {
    Name="Thunderous Roar",
    Binding=scripty.Bindings.ThunderousRoar,
    Conditions={
        sc.AbilityUsable("Thunderous Roar"),
        sc.UnitAttackable("target"),
        sc.NumberOf10YardEnemiesGreaterThan(2),
    }
}

Triggers.Prot.Shockwave = {
    Name="Shockwave",
    Binding=scripty.Bindings.Shockwave,
    Conditions={
        sc.AbilityUsable("Shockwave"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.EnemyInMeleeNeedsStun(),
        Inverse(sc.AbilityWasCastRecently("Shield Charge", 5)),
    }
}

Triggers.Prot.ShockwaveAoe = {
    Name="Shockwave",
    Binding=scripty.Bindings.Shockwave,
    Conditions={
        sc.AbilityUsable("Shockwave"),
        sc.UnitAttackable("target"),
        sc.NumberOf10YardEnemiesGreaterThan(2),
        Inverse(sc.AbilityWasCastRecently("Shield Charge", 5)),
    }
}

Triggers.Prot.ChallengingShout = {
    Name="Challenging Shout",
    Binding=scripty.Bindings.ChallengingShout,
    Conditions={
        sc.AbilityUsable("Challenging Shout"),
        sc.TargetIsReadyForInterrupt(),
        sc.AbilityOnCD("Pummel"),
    }
}

Triggers.Prot.SpellReflection = {
    Name="Spell Reflection",
    Binding=scripty.Bindings.SpellReflection,
    Conditions={
        sc.AbilityUsable("Spell Reflection"),
        sc.UnitHPDamageMagicAtLeastTakenSince("player", 1, 5),
    }
}

Triggers.Prot.Taunt = {
    Name="Taunt",
    Binding=scripty.Bindings.Taunt,
    Conditions={
        sc.AbilityUsable("Taunt"),
        sc.UnitAttackable("target"),
        sc.ProtPalaUnitNeedsTaunt("target"),
    }
}

Triggers.Prot.ThunderClap = {
    Name="Thunder Clap",
    Binding=scripty.Bindings.ThunderClap,
    Conditions={
        sc.AbilityUsable("Thunder Clap"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
    }
}

Triggers.Prot.ThunderClapAoE = {
    Name="Thunder Clap",
    Binding=scripty.Bindings.ThunderClap,
    Conditions={
        sc.AbilityUsable("Thunder Clap"),
        sc.NeedThunder(),
    }
}

Triggers.Prot.HeroicThrow = {
    Name="Heroic Throw",
    Binding=scripty.Bindings.HeroicThrow,
    Conditions={
        sc.AbilityUsable("Heroic Throw"),
        sc.UnitAttackable("target"),
        Inverse(sc.EnemyUnitWithin("target", 10)),
        Inverse(sc.EnemyUnitInMelee("target")),
    }
}

Triggers.Prot.WreckingThrow = {
    Name="Wrecking Throw",
    Binding=scripty.Bindings.WreckingThrow,
    Conditions={
        sc.AbilityUsable("Wrecking Throw"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Wrecking Throw", "target")
    }
}

Triggers.Prot.DemoralizingShout = {
    Name="Demoralizing Shout",
    Binding=scripty.Bindings.DemoralizingShout,
    Conditions={
        sc.AbilityUsable("Demoralizing Shout"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
    }
}


Triggers.Prot.IgnorePainOverFlow = {
    Name="Ignore Pain",
    Binding=scripty.Bindings.IgnorePain,
    Conditions={
        sc.AbilityUsable("Ignore Pain"),
        -- sc.NumberOfMeleeEnemiesGreaterThan(0),
        -- sc.UnitHPDamageAtLeastTakenSince("player", 3, 10),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RAGE, 65),
    }
}

Triggers.Prot.IgnorePain = {
    Name="Ignore Pain",
    Binding=scripty.Bindings.IgnorePain,
    Conditions={
        sc.AbilityUsable("Ignore Pain"),
        sc.UnitHPDamageAtLeastTakenSince("player", 3, 10),
        Inverse(sc.UnitHasAura("player", "Ignore Pain", "HELPFUL")),
    }
}

Triggers.Prot.LastStand = {
    Name="Last Stand",
    Binding=scripty.Bindings.LastStand,
    Conditions={
        sc.AbilityUsable("Last Stand"),
        sc.UnitHPLessThan("player", 30),
    }
}

Triggers.Prot.ShieldWall = {
    Name="Shield Wall",
    Binding=scripty.Bindings.ShieldWall,
    Conditions={
        sc.AbilityUsable("Shield Wall"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.UnitHasAura("player", "Avatar", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Shield Wall", "HELPFUL")),
    }
}

Triggers.Prot.Avatar = {
    Name="Avatar",
    Binding=scripty.Bindings.Avatar,
    Conditions={
        sc.AbilityUsable("Avatar"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.UnitHasAura("player", "Avatar", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Shield Wall", "HELPFUL")),
    }
}

--Holy
Triggers.Holy.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellHoly(),
    }
}

Triggers.Holy.PowerWordLife = {
    Name="Life",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.PowerWordLife,
    Conditions={
        sc.AbilityCDLessThan("Power Word: Life", 1),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
    }
}

Triggers.Holy.Halo  = {
    Name="Halo",
    Binding=scripty.Bindings.Halo,
    Conditions={
        sc.AbilityUsable("Halo"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
    }
}

Triggers.Holy.Mindbender = {
    Name="Mind Bender",
    Binding=scripty.Bindings.Mindbender,
    Conditions={
        sc.AbilityUsable("Shadowfiend"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Smite", "target"),
    }
}

Triggers.Holy.PowerWordShieldPrep = {
    Name="Shield Prep",
    Focus={name="bestPrepTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 30),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
    }
}

Triggers.Holy.DivineWord = {
    Name="Divine Word",
    Binding=scripty.Bindings.DivineWord,
    Conditions={
        sc.AbilityUsable("Divine Word"),
        sc.AbilityUsable("Holy Word: Serenity"),
        sc.SpellChargesGreaterThan("Holy Word: Serenity", 0),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
    }
}

Triggers.Holy.Lightwell = {
    Name="Lightwell",
    Binding=scripty.Bindings.Lightwell,
    Conditions={
        sc.AbilityUsable("Lightwell"),
        sc.IsUnitInCombat("player"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 95),
    }
}

Triggers.Holy.DivineWord2 = {
    Name="Divine Word",
    Binding=scripty.Bindings.DivineWord,
    Conditions={
        sc.AbilityUsable("Divine Word"),
        sc.AbilityUsable("Holy Word: Serenity"),
        sc.SpellChargesGreaterThan("Holy Word: Serenity", 1),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
    }
}

Triggers.Holy.DivineWord3 = {
    Name="Divine Word",
    Binding=scripty.Bindings.DivineWord,
    Conditions={
        sc.AbilityUsable("Divine Word"),
        sc.AbilityUsable("Holy Word: Sanctify"),
        sc.SpellChargesGreaterThan("Holy Word: Sanctify", 0),
        sc.SanctifyTargetsGreaterThan(2),
    }
}

Triggers.Holy.DivineWord4 = {
    Name="Divine Word",
    Binding=scripty.Bindings.DivineWord,
    Conditions={
        sc.AbilityUsable("Divine Word"),
        sc.AbilityUsable("Holy Word: Chastise"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Holy.PrayerOfHealing = {
    Name="Prayer of Healing",
    Binding=scripty.Bindings.PrayerOfHealing,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Prayer of Healing"),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 85),
        Inverse(sc.PlayerIsCastingAbility("Prayer of Healing")),
    }
}

Triggers.Holy.CircleOfHealing = {
    Name="Circle of Healing",
    Binding=scripty.Bindings.CircleOfHealing,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Circle of Healing"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 85),
    }
}

Triggers.Holy.Apotheosis = {
    Name="Apotheosis",
    Binding=scripty.Bindings.Apotheosis,
    Conditions={
        sc.AbilityUsable("Apotheosis"),
        Inverse(sc.SpellChargesGreaterThan("Holy Word: Serenity", 0)),
        Inverse(sc.UnitHasAura("player", "Apotheosis", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.IsUnitInCombat("player"),
    }
}

Triggers.Holy.GuardianSpirit = {
    Name="Guardian Spirit",
    Binding=scripty.Bindings.GuardianSpirit,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Guardian Spirit"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 40),
    }
}

Triggers.Holy.HolyWordSerenity = {
    Name="Holy Word: Serenity",
    Binding=scripty.Bindings.HolyWordSerenity,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Holy Word: Serenity"),
        Inverse(sc.UnitHasAura("player", "Resonant Words", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 70),
    }
}

Triggers.Holy.Premonition = {
    Name="Premonition",
    Binding=scripty.Bindings.Premonition,
    Conditions={
        sc.AbilityUsable("Premonition"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),
    }
}

Triggers.Holy.DivineHymn = {
    Name="Divine Hymn",
    Binding=scripty.Bindings.DivineHymn,
    Conditions={
        sc.AbilityUsable("Divine Hymn"),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 80),
        -- Inverse(sc.AbilityWasCastRecently("Holy Word: Salvation", 10)),
    }
}

Triggers.Holy.HolyWordSalvation = {
    Name="Holy Word: Salvation",
    Binding=scripty.Bindings.HolyWordSalvation,
    Conditions={
        sc.AbilityUsable("Holy Word: Salvation"),
        sc.IncomingAOEDamage(),
        -- Inverse(sc.AbilityWasCastRecently("Divine Hymn", 10)),
    }
}

Triggers.Holy.HolyWordSerenity2 = {
    Name="Holy Word: Serenity",
    Binding=scripty.Bindings.HolyWordSerenity,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Holy Word: Serenity"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 30),
    }
}

Triggers.Holy.HolyWordSanctify = {
    Name="Holy Word: Sanctify",
    Binding=scripty.Bindings.HolyWordSanctify,
    Conditions={
        sc.AbilityUsable("Holy Word: Sanctify"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 99),  
        Inverse(sc.UnitHasAura("player", "Resonant Words", "PLAYER | HELPFUL")),
    }
}

Triggers.Holy.HolyWordChastiseHeal = {
    Name="Holy Word: Chastise",
    Binding=scripty.Bindings.HolyWordChastise,
    Conditions={
        sc.AbilityUsable("Holy Word: Chastise"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Holy Word: Chastise", "target"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),  
        Inverse(sc.UnitHasAura("player", "Resonant Words", "PLAYER | HELPFUL")),
    }
}

Triggers.Holy.Renew = {
    Name="Renew",
    Focus={name="bestRenewTarget"},
    Binding=scripty.Bindings.Renew,
    Conditions={
        sc.AbilityUsable("Renew"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 65),
        sc.UnitHPLessThan({name="bestRenewTarget"}, 99),
    }
}

Triggers.Holy.RenewLowPriority = {
    Name="RenewLow",
    Focus={name="bestRenewTarget"},
    Binding=scripty.Bindings.Renew,
    Conditions={
        sc.AbilityUsable("Renew"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 99),
    }
}

Triggers.Holy.ShadowWordPain = {
    Name="Shadow Word: Pain",
    Binding=scripty.Bindings.ShadowWordPain,
    Conditions={
        sc.AbilityUsable("Shadow Word: Pain"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Shadow Word: Pain", 3, "HARMFUL | PLAYER"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 30),
    }
}

Triggers.Holy.EmpyrealBlaze = {
    Name="Empyreal Blaze",
    Binding=scripty.Bindings.EmpyrealBlaze,
    Conditions={
        sc.AbilityUsable("Empyreal Blaze"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Holy.GreaterHeal = {
    Name="Heal",
    Binding=scripty.Bindings.GreaterHeal,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Greater Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 70),
    }
}

Triggers.Holy.Heal = {
    Name="Heal",
    Binding=scripty.Bindings.Heal,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50)),
    }
}

Triggers.Holy.HealProc = {
    Name="Heal",
    Binding=scripty.Bindings.Heal,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.UnitHasAura("player", "Waste No Time", "HELPFUL"),
    }
}

Triggers.Holy.PrayerProc = {
    Name="Heal",
    Binding=scripty.Bindings.PrayerOfHealing,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Prayer of Healing"),
        sc.UnitHPLessThan({name="secondLowestHPUnit"}, 90),
        sc.UnitHasAura("player", "Resonant Words", "HELPFUL"),
        Inverse(sc.PlayerIsCastingAbility("Prayer of Healing")),
    }
}

Triggers.Holy.Heal2 = {
    Name="Heal",
    Binding=scripty.Bindings.Heal,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.UnitHasAura("player", "Lightweaver", "HELPFUL"),
    }
}

Triggers.Holy.HealWeaver = {
    Name="Heal",
    Binding=scripty.Bindings.FlashHeal,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Flash Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
    }
}

Triggers.Holy.HealWeaver2 = {
    Name="Heal",
    Binding=scripty.Bindings.Heal,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Heal"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 0),
        sc.UnitHasAura("player", "Lightweaver", "HELPFUL" ),
        Inverse(sc.PlayerIsCastingAbility("Heal")),
    }
}

Triggers.Holy.FlashHealResonant = {
    Name="Flash Heal",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.FlashHeal,
    Conditions={
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.AbilityUsable("Flash Heal"),
        Inverse(sc.PlayerIsCastingAbility("Flash Heal")),
        sc.UnitHasAura("player", "Resonant Words", "PLAYER | HELPFUL"),
    }
}

Triggers.Holy.FlashHeal = {
    Name="Flash Heal",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.FlashHeal,
    Conditions={
        sc.UnitHPLessThan({name="bestHealTarget"}, 80),
        sc.PlayerManaGreaterThanHPOfTarget({name="bestHealTarget"}, 20),
        sc.AbilityUsable("Flash Heal"),
        Inverse(sc.AbilityWasCastRecently("Flash Heal", 2)),
        Inverse(sc.PlayerIsCastingAbility("Flash Heal")),
        Inverse(sc.UnitHasAura("player", "Lightweaver", "HELPFUL" )),
        Inverse(sc.UnitHasAura("player", "Resonant Words", "PLAYER | HELPFUL")),
    }
}

Triggers.Holy.FlashHeal2 = {
    Name="Flash Heal",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.FlashHeal,
    Conditions={
        Inverse(sc.UnitHasAura("player", "Lightweaver", "HELPFUL")),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityUsable("Flash Heal"), 
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
    }
}

Triggers.Holy.HolyFire = {
    Name="Holy Fire",
    Binding=scripty.Bindings.HolyFire,
    Conditions={
        sc.AbilityUsable("Holy Fire"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Holy.HolyWordChastise = {
    Name="Holy Word: Chastise",
    Binding=scripty.Bindings.HolyWordChastise,
    Conditions={
        sc.AbilityUsable("Holy Word: Chastise"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Holy Word: Chastise", "target"),
        Inverse(sc.UnitHasAura("player", "Resonant Words", "PLAYER | HELPFUL")),
    }
}

--Shadow
Triggers.Shadow.ShackleGhosts = {
    Name="Shackle Undead",
    Binding=scripty.Bindings.ShackleUndead,
    Conditions={
        sc.AbilityUsable("Shackle Undead"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
        sc.AbilityInRange("Smite", "target"),
    }
}

Triggers.Shadow.DominateGhosts = {
    Name="Dominate Ghost",
    Binding=scripty.Bindings.DominateMind,
    Conditions={
        sc.AbilityUsable("Dominate Mind"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Incorporeal Being"),
        sc.AbilityInRange("Dominate Mind", "target"),
    }
}

Triggers.Shadow.PsychicScream = {
    Name="Psychic Scream",
    Binding=scripty.Bindings.PsychicScream,
    Conditions={
        sc.AbilityUsable("Psychic Scream"),
        sc.PlayerWasRecentlyMeleed(),
    }
}

Triggers.Shadow.ShadowWordDeath = {
    Name="Shadow word death",
    Binding=scripty.Bindings.ShadowWordDeath,
    Conditions={
        sc.AbilityUsable("Shadow Word: Death"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("target", 20),
        Inverse(sc.UnitHPLessThan("player", 60)),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.ShadowWordDeath2 = {
    Name="Shadow word death",
    Binding=scripty.Bindings.ShadowWordDeath,
    Conditions={
        sc.AbilityUsable("Shadow Word: Death"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Deathspeaker", "HELPFUL"),
        Inverse(sc.UnitHPLessThan("player", 60)),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.VoidTorrent = {
    Name="Void Torrent",
    Binding=scripty.Bindings.VoidTorrent,
    Conditions={
        sc.AbilityUsable("Void Torrent"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
        -- sc.AbilityInRange("Void Torrent"),
    }
}

Triggers.Shadow.ShadowWordPain = {
    Name="Shadow Word: Pain",
    Binding=scripty.Bindings.ShadowWordPain,
    Conditions={
        sc.AbilityUsable("Shadow Word: Pain"),
        sc.AbilityInRange("Vampiric Touch", "target"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Shadow Word: Pain", 3, "HARMFUL | PLAYER"),
    }
}

Triggers.Shadow.PowerWordShieldSave = {
    Name="Shield Save Me",
    Focus="player",
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 30),
        sc.UnitUnderAttack("player"),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Power Word: Shield", "PLAYER | HELPFUL")),
    }
}


Triggers.Shadow.PowerWordShieldMe = {
    Name="Shield",
    Focus="player",
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 90),
        Inverse(sc.UnitHasAura("player", "Power Word: Shield", "PLAYER | HELPFUL")),
    }
}

Triggers.Shadow.PowerWordShield = {
    Name="Shield",
    Focus={name="bestShieldTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.UnitHPLessThan({name="bestShieldTarget"}, 50),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 50),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Power Word: Shield", "PLAYER | HELPFUL")),
    }
}

Triggers.Shadow.MindBlast = {
    Name="Mind Blast",
    Binding=scripty.Bindings.MindBlast,
    Conditions={
        sc.AbilityUsable("Mind Blast"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Mind Melt", 1, "HELPFUL")),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.MindBlast2 = {
    Name="Mind Blast",
    Binding=scripty.Bindings.MindBlast,
    Conditions={
        sc.AbilityUsable("Mind Blast"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Shadowy Insight", "HELPFUL"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.VampiricEmbrace = {
    Name="Vampiric Embrace",
    Binding=scripty.Bindings.VampiricEmbrace,
    Conditions={
        sc.AbilityUsable("Vampiric Embrace"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.Damnation = {
    Name="Damnation",
    Binding=scripty.Bindings.Damnation,
    Conditions={
        sc.AbilityUsable("Damnation"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.DarkAscension = {
    Name="Dark Ascension",
    Binding=scripty.Bindings.DarkAscension,
    Conditions={
        sc.AbilityUsable("Dark Ascension"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.VoidEruption = {
    Name="Void Eruption",
    Binding=scripty.Bindings.VoidEruption,
    Conditions={
        sc.AbilityUsable("Void Eruption"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.VoidBolt = {
    Name="Void Bolt",
    Binding=scripty.Bindings.VoidBolt,
    Conditions={
        sc.AbilityUsable("Void Bolt"),
        sc.UnitAttackable("target"),
        sc.UnitHasAura("player", "Voidform", "HELPFUL"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.DevouringPlague = {
    Name="Devouring Plague",
    Binding=scripty.Bindings.DevouringPlague,
    Conditions={
        sc.AbilityUsable("Devouring Plague"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.Dispersion = {
    Name="Dispersion",
    Binding=scripty.Bindings.Dispersion,
    Conditions={
        sc.AbilityUsable("Dispersion"),
        sc.UnitHPLessThan("player", 35),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
    }
}

Triggers.Shadow.MindSpike = {
    Name="Mind Spike",
    Binding=scripty.Bindings.MindSpike,
    Conditions={
        sc.AbilityUsable("Mind Spike"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
    }
}

Triggers.Shadow.Shadowform = {
    Name="Shadowform",
    Binding=scripty.Bindings.Shadowform,
    Conditions={
        sc.AbilityUsable("Shadowform"),
        Inverse(sc.UnitHasAura("player", "Shadowform", "HELPFUL")),
    }
}

Triggers.Disc.RenewLowPriority = {
    Name="RenewLow",
    Focus={name="bestRenewTarget"},
    Binding=scripty.Bindings.Renew,
    Conditions={
        sc.AbilityUsable("Renew"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 99),
        sc.AbilityInRange("Renew", {name="bestRenewTarget"}),
    }
}

Triggers.Shadow.Silence = {
    Name="Silence",
    Binding=scripty.Bindings.Silence,
    Conditions={
        sc.AbilityUsable("Silence"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.Shadow.VampiricTouch = {
    Name="Vampiric Touch",
    Binding=scripty.Bindings.VampiricTouch,
    Conditions={
        sc.AbilityUsable("Vampiric Touch"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Vampiric Touch", "target"),
        Inverse(sc.UnitHasAuraOrSpell("target", "Vampiric Touch", "HARMFUL | PLAYER")),
    }
}

--Disc
Triggers.Disc.PurgeExplosives = {
    Name="Purge Explosives",
    Binding=scripty.Bindings.PurgeTheWicked,
    Conditions={
        sc.AbilityUsable("Purge the Wicked"),
        sc.UnitAttackable("target"),
        sc.UnitNameIs("target", "Explosives"),
        sc.AbilityInRange("Smite", "target"),
    }
}

Triggers.Disc.PenanceHeal = {
    Name="healPenance",
    Binding=scripty.Bindings.Penance,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Penance"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 10),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 5)),
    }
}

Triggers.Disc.UltimatePenitence = {
    Name="Ultimate Penitence",
    Binding=scripty.Bindings.UltimatePenitence,
    Conditions={
        sc.AbilityUsable("Ultimate Penitence"),
        sc.AbilityInRange("Smite", "target"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 10),
        Inverse(sc.AbilityWasCastRecently("Mindbender", 15)),
    }
}

Triggers.Disc.PenanceHealLowPriority = {
    Name="low priority penance",
    Binding=scripty.Bindings.Penance,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Penance"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 10),
    }
}

Triggers.Disc.Penance = {
    Name="attackPenance",
    Binding=scripty.Bindings.PenanceAttack,
    Conditions={
        sc.AbilityUsable("Penance"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 10),
        sc.AbilityInRange("Smite", "target"),

    }
}

Triggers.Disc.VoidTendrils = {
    Name="Void Tendrils",
    Binding=scripty.Bindings.VoidTendrils,
    Conditions={
        sc.AbilityUsable("Void Tendrils"),
        sc.PlayerWasRecentlyMeleed(),
    }
}

Triggers.Disc.ShadowWordDeath = {
    Name="Shadow word death",
    Binding=scripty.Bindings.ShadowWordDeath,
    Conditions={
        sc.AbilityUsable("Shadow Word: Death"),
        sc.UnitAttackable("target"),
        sc.UnitHPLessThan("target", 20),
        Inverse(sc.UnitHPLessThan("player", 60)),
        sc.AbilityInRange("Smite", "target"),
    }
}

Triggers.Disc.DesperatePrayer = {
    Name="Prayer",
    Binding=scripty.Bindings.DesperatePrayer,
    Conditions={
        sc.AbilityUsable("Desperate Prayer"),
        sc.UnitHPLessThan("player", 65),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
    }
}

Triggers.Disc.Fleshcraft = {
    Name="Fleshcraft",
    Binding=scripty.Bindings.Fleshcraft,
    Conditions={
        sc.AbilityUsable("Fleshcraft"),
        Inverse(sc.PlayerIsDoingSomething()),
    }
}

Triggers.Disc.Fade = {
    Name="Fade",
    Binding=scripty.Bindings.Fade,
    Conditions={
        sc.AbilityUsable("Fade"),
        sc.PlayerIsThreatened(),
        sc.AvoidingPenance(),
        Inverse(sc.PlayerIsDoingSomething()),
    }
}

Triggers.Disc.PowerInfusion = {
    Name="Power Infusion",
    Binding=scripty.Bindings.PowerInfusion,
    Focus={name="bestPITarget"},
    Conditions={
        sc.AbilityUsable("Power Infusion"),
        sc.UnitAttackable("target"),
      --  sc.UnitHasAura("player", "Shadow Covenant", "HELPFUL | PLAYER"),
        Inverse(sc.PlayerIsDoingSomething()),
        sc.AvoidingPenance(),
    }
}

Triggers.Disc.Feather = {
    Name="Feather",
    Binding=scripty.Bindings.Feather,
    Conditions={
        sc.AbilityUsable("Angelic Feather"),
        sc.UnitSpeedLessThan("player", 8),
        sc.PlayerMoving(),
    }
}

Triggers.Disc.PurgeTheWicked = {
    Name="Purge the Wicked",
    Binding=scripty.Bindings.PurgeTheWicked,
    Conditions={
        sc.AbilityUsable("Purge the Wicked"),
        sc.UnitAttackable("target"),
        sc.UnitAuraDurationLessThan("target", "Purge the Wicked", 1, "HARMFUL | PLAYER"),
        sc.AbilityInRange("Smite", "target"),
        -- sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
        -- Inverse(sc.AbilityWasCastRecently("Purge the Wicked", 8)),
    }
}

Triggers.Disc.UnholyNova = {
    Name="Unholy Nova",
    Binding=scripty.Bindings.UnholyNova,
    Conditions={
        sc.AbilityUsable("Unholy Nova"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Disc.Mindbender = {
    Name="Mind Bender",
    Binding=scripty.Bindings.Mindbender,
    Conditions={
        sc.AbilityUsable("Shadowfiend"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Smite", "target"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
    }
}

Triggers.Disc.Mindbender2 = {
    Name="Mind Bender",
    Binding=scripty.Bindings.Mindbender,
    Conditions={
        sc.AbilityUsable("Shadowfiend"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Smite", "target"),
        sc.IncomingAOEDamage(),
    }
}

Triggers.Disc.Mindgames = {
    Name="Mind Games",
    Binding=scripty.Bindings.Mindgames,
    Conditions={
        sc.AbilityUsable("Mindgames"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Smite", "target"),
       -- sc.UnitHasAura("player", "Shadow Covenant", "HELPFUL | PLAYER"),
    }
}

Triggers.Disc.Schism = {
    Name="Schism",
    Binding=scripty.Bindings.Schism,
    Conditions={
        sc.AbilityUsable("Schism"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Smite", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.Disc.ShadowCovenant = {
    Name="Shadow Covenant",
    Binding=scripty.Bindings.ShadowCovenant,
    Focus={name="bestHealTarget"},
    Conditions={
        sc.AbilityUsable("Shadow Covenant"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.Disc.MindBlast = {
    Name="Mind Blast",
    Binding=scripty.Bindings.MindBlast,
    Conditions={
        sc.AbilityUsable("Mind Blast"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Smite", "target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 80),
    }
}

Triggers.Disc.Halo  = {
    Name="Halo",
    Binding=scripty.Bindings.Halo,
    Conditions={
        sc.AbilityUsable("Halo"),
        sc.UnitAttackable("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 60),
    }
}

Triggers.Disc.Smite = {
    Name="Smite",
    Binding=scripty.Bindings.Smite,
    Conditions={
        sc.AbilityUsable("Smite"),
        sc.AbilityInRange("Smite", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.Disc.FlashHealProc = {
    Name="Flash Heal",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.FlashHeal,
    Conditions={
        sc.UnitHPLessThan({name="bestHealTarget"}, 90),
        sc.AbilityUsable("Flash Heal"),
        sc.UnitHasAura("player", "Surge of Light", "HELPFUL"),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Flash Heal", 2)),
        Inverse(sc.PlayerIsCastingAbility("Flash Heal")),
    }
}

Triggers.Disc.FlashHealProc2 = {
    Name="Flash Heal",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.FlashHeal,
    Conditions={
        sc.UnitHPLessThan({name="bestHealTarget"}, 35),
        sc.AbilityUsable("Flash Heal"),
        sc.UnitHasAura("player", "Surge of Light", "HELPFUL"),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Flash Heal", 2)),
        Inverse(sc.PlayerIsCastingAbility("Flash Heal")),
    }
}

Triggers.Disc.FlashHeal = {
    Name="Flash Heal",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.FlashHeal,
    Conditions={
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        sc.AbilityUsable("Flash Heal"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 30),
        Inverse(sc.AbilityWasCastRecently("Flash Heal", 2)),
        Inverse(sc.PlayerIsCastingAbility("Flash Heal")),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Atonement", "PLAYER | HELPFUL")),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
    }
}

Triggers.Disc.PrayerOfMending = {
    Name="POM",
    Focus={name="bestPOMTarget"} ,
    Binding=scripty.Bindings.PrayerOfMending,
    Conditions={
        sc.AbilityUsable("Prayer of Mending"),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
    }
}

Triggers.Disc.PowerWordShield = {
    Name="Shield",
    Focus={name="bestShieldTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        sc.UnitHPLessThan({name="bestShieldTarget"}, 65),
    }
}

Triggers.Disc.PowerWordShieldPrep = {
    Name="Shield",
    Focus={name="bestPrepTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        Inverse(sc.UnitHPLessThan({name="bestHealTarget"}, 65)),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
    }
}

Triggers.Disc.PowerWordShieldPrep2 = {
    Name="Shield",
    Focus={name="bestShieldTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        Inverse(sc.UnitHPLessThan({name="bestHealTarget"}, 65)),
        sc.IncomingAOEDamage(),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
    }
}

Triggers.Disc.PowerWordLife = {
    Name="Life",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.PowerWordLife,
    Conditions={
        sc.AbilityUsable("Power Word: Life"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 35),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
    }
}

Triggers.Disc.Rapture = {
    Name="Rapture",
    Focus={name="bestShieldTarget"},
    Binding=scripty.Bindings.Rapture,
    Conditions={
        sc.AbilityUsable("Rapture"),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        sc.UnitHPLessThan({name="bestShieldTarget"}, 65),
        sc.MissingDiscPanic(),
    }
}

Triggers.Disc.RapturePrep = {
    Name="Rapture",
    Focus={name="bestPrepTarget"},
    Binding=scripty.Bindings.Rapture,
    Conditions={
        sc.AbilityUsable("Rapture"),
        sc.MissingDiscPanic(),
        sc.IncomingAOEDamage(),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
    }
}

Triggers.Disc.RenewPrep = {
    Name="Renew",
    Focus={name="bestAtonementTarget"},
    Binding=scripty.Bindings.Renew,
    Conditions={
        sc.AbilityUsable("Renew"),
        sc.IncomingAOEDamage(),
        Inverse(sc.UnitHasAura({name="bestAtonementTarget"}, "Renew", "PLAYER | HELPFUL")),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
    }
}

Triggers.Disc.LuminousBarrier = {
    Name="Luminous Barrier",
    Binding=scripty.Bindings.LuminousBarrier,
    Conditions={
        sc.AbilityUsable("Luminous Barrier"),
        sc.IncomingAOEDamage(),
        sc.MissingDiscPanic(),
    }
}

Triggers.Disc.LuminousBarrier2 = {
    Name="Luminous Barrier",
    Binding=scripty.Bindings.LuminousBarrier,
    Conditions={
        sc.AbilityUsable("Luminous Barrier"),
        sc.DamagingDebuffsGreaterThan(3),
        sc.MissingDiscPanic(),
    }
}

Triggers.Disc.PainSuppression = {
    Name="Pain Suppression",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.PainSuppression,
    Conditions={
        sc.AbilityUsable("Pain Suppression"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Pain Suppression", "PLAYER | HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rapture", "HELPFUL")),
        sc.IncomingAOEDamage(),
        sc.AvoidingPenance(),
    }
}

Triggers.Disc.PainSuppression2 = {
    Name="Pain Suppression",
    Focus={name="bestHealTarget"} ,
    Binding=scripty.Bindings.PainSuppression,
    Conditions={
        sc.AbilityUsable("Pain Suppression"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 50),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Pain Suppression", "PLAYER | HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Rapture", "HELPFUL")),
        sc.UnitIsUnit({name="bestHealTarget"}, {name="bestPrepTarget"}),
        sc.AvoidingPenance(),
    }
}

Triggers.Disc.Purify = {
    Name="Purify",
    Binding=scripty.Bindings.Purify,
    Focus={name="purifyTarget"},
    Conditions={
        sc.AbilityUsable("Purify"),
        sc.AbilityInRange("Flash Heal", {name="purifyTarget"}),
    }
}

Triggers.Disc.PowerWordFortitude = {
    Name="Fortitude",
    Focus="player",
    Binding=scripty.Bindings.PowerWordFortitude,
    Conditions={
        sc.AbilityUsable("Power Word: Fortitude"),
        sc.FortitudeNeeded(),
    }
}

Triggers.Disc.Renew = {
    Name="Renew",
    Focus={name="bestAtonementTarget"},
    Binding=scripty.Bindings.Renew,
    Conditions={
        sc.AbilityUsable("Renew"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 99),
        Inverse(sc.UnitHasAura({name="bestAtonementTarget"}, "Renew", "PLAYER | HELPFUL")),
    }
}

Triggers.Disc.RenewLowPriority = {
    Name="RenewLow",
    Focus={name="bestAtonementTarget"},
    Binding=scripty.Bindings.Renew,
    Conditions={
        sc.AbilityUsable("Renew"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 99),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
    }
}

Triggers.Disc.HolyNovaProc = {
    Name="Holy Nova",
    Binding=scripty.Bindings.HolyNova,
    Conditions={
        sc.AbilityUsable("Holy Nova"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
        Inverse(sc.UnitAuraCountLessThan("player", "Rhapsody", 15, "PLAYER | HELPFUL")),
    }
}

Triggers.Disc.HolyNova = {
    Name="Holy Nova",
    Binding=scripty.Bindings.HolyNova,
    Conditions={
        sc.AbilityUsable("Holy Nova"),
        Inverse(sc.UnitAuraCountLessThan("player", "Rhapsody", 15, "PLAYER | HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 30),
        sc.UnitHPLessThan("player", 99),
    }
}

Triggers.Disc.PowerWordShieldLowPriority = {
    Name="Shield low priority",
    Focus={name="bestAtonementTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 99),
    }
}

Triggers.Disc.PowerWordShieldSave = {
    Name="Shield Save",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.UnitHPLessThan({name="bestHealTarget"}, 65),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Power Word: Shield", "PLAYER | HELPFUL")),
    }
}

Triggers.Disc.PowerWordShieldSpeed = {
    Name="Shield speed",
    Focus="player",
    Binding=scripty.Bindings.PowerWordShield,
    Conditions={
        sc.AbilityUsable("Power Word: Shield"),
        sc.UnitSpeedLessThan("player", 10),
        sc.PlayerBeenMoving(),
        Inverse(sc.UnitHasAura("player", "Power Word: Shield", "PLAYER | HELPFUL")),
        Inverse(sc.UnitHasAura("player", "Vampiric Embrace", "HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 96),
    }
}

Triggers.Disc.RadianceForAtonement = {
    Name="Radiance",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.PowerWordRadiance,
    Conditions={
        sc.AbilityUsable("Power Word: Radiance"),
        Inverse(sc.UnitHasAura({name="bestHealTarget"}, "Atonement", "PLAYER | HELPFUL")),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        sc.RadianceTargetsGreaterThan(2),
        Inverse(sc.UnitHasAura("player", "Rapture", "HELPFUL")),
    }
}

Triggers.Disc.RadianceForHealth = {
    Name="Radiance",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.PowerWordRadiance,
    Conditions={
        sc.AbilityUsable("Power Word: Radiance"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        sc.UnitHPLessThan({name="thirdLowestHPUnit"}, 65),
        Inverse(sc.UnitHasAura("player", "Rapture", "HELPFUL")),
    }
}
 
Triggers.Disc.RadianceOnTime = {
    Name="Radiance",
    Focus={name="bestHealTarget"},
    Binding=scripty.Bindings.PowerWordRadiance,
    Conditions={
        sc.AbilityUsable("Power Word: Radiance"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
        Inverse(sc.PlayerIsCastingAbility("Power Word: Radiance")),
        Inverse(sc.AbilityWasCastRecently("Power Word: Radiance", 2)),
        sc.SecondsUntilAOELessThan(0.75),
        Inverse(sc.UnitHasAura("player", "Rapture", "HELPFUL")),
    }
}

Triggers.Disc.RadianceRaid = {
    Name="Radiance R",
    Focus={name="bestAtonementTarget"},
    Binding=scripty.Bindings.PowerWordRadiance,
    Conditions={
        sc.AbilityUsable("Power Word: Radiance"),
        sc.RadianceTargetsGreaterThan(4),
        sc.PlayerResourceMinimum(SOPHIE_POWER_MANA, 20),
    }
}

Triggers.Disc.StopCasting = {
    Name="interrupt",
    Binding=scripty.Bindings.StopCasting,
    Conditions={
        sc.ShouldInterruptCurrentSpellDisc(),
    }
}


local SophieDKMinimumBoneShieldCharges = 9
local SophieDKPanicButtonHealth = 35

--Sophie Death Knight
Triggers.SophieDK.DeathsCaressExplosive = {
    Name="Death's Caress",
    Binding=scripty.Bindings.DeathsCaress,
    Conditions={
        sc.AbilityUsable("Death's Caress"),
        sc.UnitNameIs("target", "Explosives"),
    }
}
Triggers.SophieDK.DeathsCaress = {
    Name="Death's Caress",
    Binding=scripty.Bindings.DeathsCaress,
    Conditions={
        sc.AbilityUsable("Death's Caress"),
        sc.UnitAttackable("target"),
        sc.UnitAuraCountLessThan("player", "Bone Shield", SophieDKMinimumBoneShieldCharges, "HELPFUL"),
    }
}
Triggers.SophieDK.EmpowerRuneWeapon = {
    Name="Empower Rune Weapon",
    Binding=scripty.Bindings.EmpowerRuneWeapon,
    Conditions={
        sc.AbilityUsable("Empower Rune Weapon"),
        sc.UnitAttackable("target"),
        Inverse(sc.DeathKnightRunesGreaterThan(5)),
    }
}
Triggers.SophieDK.Marrowrend = {
    Name="Marrowrend",
    Binding=scripty.Bindings.Marrowrend,
    Conditions={
        sc.AbilityUsable("Marrowrend"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAuraCountLessThan("player", "Bone Shield", SophieDKMinimumBoneShieldCharges, "HELPFUL"),
    }
}

Triggers.SophieDK.Marrowrend2 = {
    Name="Marrowrend",
    Binding=scripty.Bindings.Marrowrend,
    Conditions={
        sc.AbilityUsable("Marrowrend"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAuraCountLessThan("player", "Bone Shield", 3, "HELPFUL"),
    }
}

Triggers.SophieDK.DancingRuneWeapon = {
    Name="Dancing Rune Weapon",
    Binding=scripty.Bindings.DancingRuneWeapon,
    Conditions={
        sc.AbilityUsable("Dancing Rune Weapon"),
        sc.UnitAttackable("target"),
        sc.AbilityInRange("Dancing Rune Weapon", "target"),
    }
}

Triggers.SophieDK.VampiricBlood = {
    Name="Vampiric Blood",
    Binding=scripty.Bindings.VampiricBlood,
    Conditions={
        sc.AbilityUsable("Vampiric Blood"),
        sc.IsUnitInCombat("player"),
        sc.EnemyUnitInMelee("target"),
        -- sc.UnitHPLessThan("player", SophieDKPanicButtonHealth),
    }
}

Triggers.SophieDK.VampiricBlood2 = {
    Name="Vampiric Blood",
    Binding=scripty.Bindings.VampiricBlood,
    Conditions={
        sc.AbilityUsable("Vampiric Blood"),
        sc.IsUnitInCombat("player"),
        sc.UnitHPLessThan("player", 99),
    }
}


Triggers.SophieDK.RuneTap = {
    Name="Rune Tap 2 charges",
    Binding=scripty.Bindings.RuneTap,
    Conditions={
        sc.AbilityUsable("Rune Tap"),
        Inverse(sc.UnitHasAura("player", "Rune Tap", "HELPFUL")),
        sc.UnitHPDamageAtLeastTakenSince("player", 50, 5),
        sc.SpellChargesGreaterThan("Rune Tap", 1),
    }
}

Triggers.SophieDK.RuneTap2 = {
    Name="Rune Tap",
    Binding=scripty.Bindings.RuneTap,
    Conditions={
        sc.AbilityUsable("Rune Tap"),
        Inverse(sc.UnitHasAura("player", "Rune Tap", "HELPFUL")),
        sc.UnitHPDamageAtLeastTakenSince("player", 80, 5),
    }
}

Triggers.SophieDK.BloodTap = {
    Name="Blood Tap",
    Binding=scripty.Bindings.BloodTap,
    Conditions={
        sc.AbilityUsable("Blood Tap"),
        Inverse(sc.DeathKnightRunesGreaterThan(1)),
    }
}

Triggers.SophieDK.RaiseDead = {
    Name="Raise Dead",
    Binding=scripty.Bindings.RaiseDead,
    Conditions={
        sc.AbilityUsable("Raise Dead"),
        sc.UnitAttackable("target"),
    }
}

Triggers.SophieDK.DarkCommand = {
    Name="Dark Command",
    Binding=scripty.Bindings.DarkCommand,
    Conditions={
        sc.AbilityUsable("Dark Command"),
        sc.AbilityInRange("Dark Command", "target"),
        sc.UnitAttackable("target"),
        sc.ProtPalaUnitNeedsTaunt("target"),
    }
}

Triggers.SophieDK.SoulReaper = {
    Name="Soul Reaper",
    Binding=scripty.Bindings.SoulReaper,
    Conditions={
        sc.AbilityUsable("Soul Reaper"),
        sc.UnitHPLessThan("target", 37),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
    }
}

Triggers.SophieDK.DeathStrike = {
    Name="Death Strike to prevent cap",
    Binding=scripty.Bindings.DeathStrike,
    Conditions={
        sc.AbilityUsable("Death Strike"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.PlayerResourceMinimum(SOPHIE_POWER_RUNICPOWER, 80),
    }
}

Triggers.SophieDK.DeathStrike2 = {
    Name="Death Strike to heal",
    Binding=scripty.Bindings.DeathStrike,
    Conditions={
        sc.AbilityUsable("Death Strike"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHPLessThan("player", 65),
    }
}

Triggers.SophieDK.DeathStrike3 = {
    Name="Death Strike to heal",
    Binding=scripty.Bindings.DeathStrike,
    Conditions={
        sc.AbilityUsable("Death Strike"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHPLessThan("player", 40),
    }
}

Triggers.SophieDK.Tombstone = {
    Name="Tombstone",
    Binding=scripty.Bindings.Tombstone,
    Conditions={
        sc.AbilityUsable("Tombstone"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitHasAura("player", "Death and Decay", "PLAYER | HELPFUL"),
        Inverse(sc.UnitAuraCountLessThan("player", "Bone Shield", 5, "HELPFUL")),
        Inverse(sc.AbilityCDLessThan("Dancing Rune Weapon", 10)),
        Inverse(sc.PlayerResourceMinimum(SOPHIE_POWER_RUNICPOWER, 65)),
    }
}

Triggers.SophieDK.DeathAndDecay = {
    Name="Death and Decay",
    Binding=scripty.Bindings.DeathAndDecay,
    Conditions={
        sc.AbilityUsable("Death and Decay"),
        sc.UnitAttackable("target"),
        sc.EnemyUnitInMelee("target"),
        Inverse(sc.UnitHasAura("player", "Death and Decay", "PLAYER | HELPFUL")),
    }
}

Triggers.SophieDK.BloodBoil = {
    Name="Blood Boil",
    Binding=scripty.Bindings.BloodBoil,
    Conditions={
        sc.AbilityUsable("Blood Boil"),
        sc.DeathKnightBloodBoilIsNeeded(),
    }
}

Triggers.SophieDK.BloodBoil2 = {
    Name="Blood Boil",
    Binding=scripty.Bindings.BloodBoil,
    Conditions={
        sc.AbilityUsable("Blood Boil"),
        sc.NumberOfMeleeEnemiesGreaterThan(0),
    }
}

Triggers.SophieDK.HeartStrike = {
    Name="Heart Strike",
    Binding=scripty.Bindings.HeartStrike,
    Conditions={
        sc.AbilityUsable("Heart Strike"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAttackable("target"),
        sc.DeathKnightRunesGreaterThan(1),
    }
}

Triggers.SophieDK.AbominationLimb = {
    Name="Abomination Limb",
    Binding=scripty.Bindings.AbominationLimb,
    Conditions={
        sc.AbilityUsable("Abomination Limb"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.SophieDK.AntiMagicShell = {
    Name="Anti-Magic Shell",
    Binding=scripty.Bindings.AntiMagicShell,
    Conditions={
        sc.AbilityUsable("Anti-Magic Shell"),
        sc.UnitHPDamageMagicAtLeastTakenSince("player", 1, 5),
    }
}

Triggers.SophieDK.Asphyxiate = {
    Name="Asphyxiate",
    Binding=scripty.Bindings.Asphyxiate,
    Conditions={
        sc.AbilityUsable("Asphyxiate"),
        sc.AbilityInRange("Chains of Ice", "target"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
    }
}

Triggers.SophieDK.BlindingSleet = {
    Name="Blinding Sleet",
    Binding=scripty.Bindings.BlindingSleet,
    Conditions={
        sc.AbilityUsable("Blinding Sleet"),
        sc.EnemyUnitWithin("target", 10),
        sc.StunnableUnitCasting("target"),
        Inverse(sc.UnitHasAura("target", "Chains of Ice", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Blinding Sleet", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Blessing of Freedom", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Aspect of the Turtle", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Divine Shield", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Anti-Magic Shell", "HELPFUL")),
    }
}

Triggers.SophieDK.ChainsOfIce = {
    Name="Chains of Ice",
    Binding=scripty.Bindings.ChainsOfIce,
    Conditions={
        sc.AbilityUsable("Chains of Ice"),
        sc.AbilityInRange("Chains of Ice", "target"),
        Inverse(sc.UnitHasAura("target", "Chains of Ice", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Blinding Sleet", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Blessing of Freedom", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Aspect of the Turtle", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Divine Shield", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Anti-Magic Shell", "HELPFUL")),
        sc.UnitIsPlayer("target"),
    }
}

Triggers.SophieDK.ChainsOfIce = {
    Name="Chains of Ice",
    Binding=scripty.Bindings.ChainsOfIce,
    Conditions={
        sc.AbilityUsable("Chains of Ice"),
        sc.AbilityInRange("Chains of Ice", "target"),
        Inverse(sc.UnitHasAura("target", "Chains of Ice", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Blinding Sleet", "HARMFUL")),
        Inverse(sc.UnitHasAura("target", "Blessing of Freedom", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Aspect of the Turtle", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Divine Shield", "HELPFUL")),
        Inverse(sc.UnitHasAura("target", "Anti-Magic Shell", "HELPFUL")),
        sc.UnitIsPlayer("target"),
    }
}

Triggers.SophieDK.MindFreeze = {
    Name="Mind Freeze",
    Binding=scripty.Bindings.MindFreeze,
    Conditions={
        sc.AbilityUsable("Mind Freeze"),
        sc.AbilityInRange("Mind Freeze", "target"),
        sc.TargetIsReadyForInterrupt(),
    }
}

Triggers.SophieDK.Blooddrinker = {
    Name="Blooddrinker",
    Binding=scripty.Bindings.Blooddrinker,
    Conditions={
        sc.AbilityUsable("Blooddrinker"),
        sc.AbilityInRange("Blooddrinker", "target"),
        sc.UnitAttackable("target"),
    }
}

Triggers.SophieDK.DeathGrip = {
    Name="Death Grip",
    Binding=scripty.Bindings.DeathGrip,
    Conditions={
        sc.AbilityUsable("Death Grip"),
        sc.AbilityInRange("Death Grip", "target"),
        sc.UnitAttackable("target"),
        sc.ProtPalaUnitNeedsTaunt("target"),
    }
}

Triggers.SophieDK.DeathGrip2 = {
    Name="Death Grip",
    Binding=scripty.Bindings.DeathGrip,
    Conditions={
        sc.AbilityUsable("Death Grip"),
        sc.AbilityInRange("Death Grip", "target"),
        sc.UnitAttackable("target"),
        sc.StunnableUnitCasting("target"),
        Inverse(sc.EnemyUnitInMelee("target")),
    }
}

Triggers.SophieDK.Bonestorm = {
    Name="Bonestorm",
    Binding=scripty.Bindings.Bonestorm,
    Conditions={
        sc.AbilityUsable("Bonestorm"),
        sc.EnemyUnitInMelee("target"),
        sc.UnitAttackable("target"),
        Inverse(sc.UnitAuraCountLessThan("player", "Bone Shield", 10, "HELPFUL")),
    }
}

Triggers.SophieDK.DeathPact = {
    Name="Death Pact",
    Binding=scripty.Bindings.DeathPact,
    Conditions={
        sc.AbilityUsable("Death Pact"),
        sc.UnitHPLessThan("player", 30),
    }
}

Triggers.SophieDK.WraithWalk = {
    Name="Death Pact",
    Binding=scripty.Bindings.DeathPact,
    Conditions={
        function() return false end,
    }
}

scripty.Triggers = Triggers